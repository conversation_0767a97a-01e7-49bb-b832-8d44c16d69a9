---
id: more-resources
title: More Resources
---

There’s always more to learn: developer workflows, shipping to app stores, internationalization, security and more.

## Where to go from here

- [Set up your environment](environment-setup)
- [Set up your development workflow](running-on-device)
- [Design and layout your app](flexbox)
- [Debug your app](debugging)
- [Make your app cross platform](platform-specific-code)
- [Get involved in the React Native community](/community/overview)

## Dive deep

- [React’s Documentation](https://react.dev/learn)
- [MDN’s JavaScript tutorials, reference, and guides](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
- [Android](https://developer.android.com/docs) and [iOS](https://developer.apple.com/documentation/uikit) platform docs

## IDEs

We recommend using the [VS Code](https://code.visualstudio.com/) code editor and its handy [React Native tools](https://marketplace.visualstudio.com/items?itemName=msjsdiag.vscode-react-native).

## Platforms to try

[Expo](https://docs.expo.dev/) is a framework of tools and services for React Native that focuses on helping you build, ship, and iterate on your app, to use preview deployment workflows that are popular with web development, and to automate your development workflows. Expo also makes it possible to build React Native apps without ever touching Xcode or Android Studio, and it doesn't get in the way if you want to use those tools.

[Ignite](https://github.com/infinitered/ignite) is a starter kit CLI with several React Native boilerplates. The latest, Ignite Maverick, uses MobX-State-Tree for state management, React Navigation, and other common libraries. It has generators for screens, models, and more, and supports Expo out of the box. Ignite also comes with a component library that is tuned for custom designs, theming support, and testing. If you are looking for a preconfigured tech stack, Ignite could be perfect for you.

## Example Apps

Try out apps from the [Showcase](https://reactnative.dev/showcase) to see what React Native is capable of! Looking for something more hands on? Check out this [set of example apps on GitHub](https://github.com/ReactNativeNews/React-Native-Apps). You can look at their source code—try running one on a simulator or device.

## Find, make, and share your own Native Components and TurboModules

React Native has a community of thousands of developers like you making content, tools, tutorials—and Native Components!

Can’t find what you’re looking for in the Core Components? Visit [React Native Directory](https://reactnative.directory) to find what the community has been creating.

:::caution
This documentation references a legacy set of API and needs to be updated to reflect the New Architecture
:::
Interested in making your own Native Component or Module? Making modules for your own use case and sharing them with others on NPM and GitHub helps grow the React Native ecosystem and community! Read the guides to making your own Native Modules ([Android](legacy/native-modules-android.md), [iOS](legacy/native-modules-ios.md)) and Native Components ([Android](legacy/native-components-android.md), [iOS](legacy/native-components-ios.md)).
