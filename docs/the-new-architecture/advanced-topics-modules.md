# Advanced Topics on Native Modules Development

This document contains a set of advanced topics to implement more complex functionalities of Native Modules. It is recommended to first read the [Codegen](/docs/the-new-architecture/what-is-codegen) section and the guides on [Native Modules](/docs/turbo-native-modules-introduction).

This guide will cover the following topics:

- [Add custom C++ types to your C++ modules](/docs/the-new-architecture/custom-cxx-types)
- [Use Swift in your Module](/docs/next/the-new-architecture/turbo-modules-with-swift)
- [Emit custom events from your Native Modules](/docs/next/the-new-architecture/native-modules-custom-events)
- [Native Modules Lifecycle](/docs/next/the-new-architecture/native-modules-lifecycle)
