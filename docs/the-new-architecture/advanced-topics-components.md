# Advanced Topics on Native Modules Development

This document contains a set of advanced topics to implement more complex functionalities of Native Components. It is recommended to first read the [Codegen](/docs/the-new-architecture/what-is-codegen) section and the guides on [Native Components](/docs/fabric-native-components-introduction).

This guide will cover the following topics:

- [Direct Manipulation](/docs/the-new-architecture/direct-manipulation-new-architecture)
- [Measuring the Layout](/docs/the-new-architecture/layout-measurements)
- [Invoking native functions on your native component](/docs/next/the-new-architecture/fabric-component-native-commands)
