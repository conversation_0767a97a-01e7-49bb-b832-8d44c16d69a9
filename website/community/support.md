---
title: Where to get help
description: Where to get help
---

If you need help with your React Native app, the right place to go depends on the type of help that you need.

### Repository

The core of **React Native** is worked on full-time by Meta's React Native team. But there are far more people in the community who make key contributions and fix things. If the issue you are facing is code related, you should check the open issues in the [main repository](https://github.com/facebook/react-native/issues).

If you cannot find an existing issue, please refer to [How to report a bug](/contributing/how-to-report-a-bug).

### Upgrade support

Many times, when upgrading your apps and libraries from a version of React Native to a newer one, you might need some help; the community has rallied together to create a couple of important resources:

- [upgrade-helper](https://react-native-community.github.io/upgrade-helper/) is a tool that will show the full set of changes happening between any two versions to help you see what changes you need to do in your code.
- [upgrade support](https://github.com/react-native-community/upgrade-support) is a community-driven repository to request and give help when upgrading your app.

### Stack Overflow

Many members of the community use Stack Overflow to ask questions. Read through the [existing questions](https://stackoverflow.com/questions/tagged/react-native?sort=frequent) tagged with **react-native** or [ask your own](https://stackoverflow.com/questions/ask?tags=react-native)!

### Reactiflux Chat

If you need an answer right away, check out the [Reactiflux Discord](https://discord.gg/JuTwWB8rsy) community. There are usually a number of React Native experts there who can help out or point you to somewhere you might want to look.
