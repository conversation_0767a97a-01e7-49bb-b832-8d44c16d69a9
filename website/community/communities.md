---
title: Communities
description: The React Native Community
---

The React Native ecosystem is far and wide, and people can be part of it in many forms; here you will find but a partial list of different ways one developer can be part of it. If you know of other, or want to help expand this page, [submit a PR](https://github.com/facebook/react-native-website/pulls?q=is%3Apr+is%3Aopen+sort%3Aupdated-desc)!

### Local communities

There are a lot of React Native gatherings that happen around the world. Often there is React Native content in React meetups as well, use tools like [Meetup](https://www.meetup.com/topics/react-native/) and [Eventbrite](https://www.eventbrite.co.uk/d/online/react-native/?page=1) to find out recent events in your area - or start one!

### Company-based communities

Some companies actively involved in the React Native have also their own communication channels focused towards the projects they maintain:

- [Callstack.io's](https://www.callstack.com/) [Discord server](https://discordapp.com/invite/zwR2Cdh)
- [Invertase.io's (e.g. React Native Firebase)](https://invertase.io/) [Discord server](https://discord.gg/C9aK28N)
- [Infinite Red's](https://infinite.red/) [Slack Group](https://community.infinite.red/)
- [Expo's](https://expo.dev/) [Discord server](https://chat.expo.dev/)

### Independent communities

Organic communities of React Native developers, not sponsored by a business with a commercial interest.

- [The Reactiflux community](https://reactiflux.com) on [Discord](https://discord.gg/reactiflux)

### Content sharing

React Native tagged content can be found on many platforms, such as:

- [DevTo community](https://dev.to/t/reactnative)
- [Medium](https://medium.com/tag/react-native)
- [Hashnode](https://hashnode.com/n/react-native)
- [Hacker News](https://hn.algolia.com/?q=react-native)
- [r/reactnative/](https://www.reddit.com/r/reactnative/)

These are places where you can share React Native projects, articles and tutorials as well as start discussions and ask for feedback on React Native related topics. (but remember to give some love to the [main documentation](https://github.com/facebook/react-native-website) too!)
