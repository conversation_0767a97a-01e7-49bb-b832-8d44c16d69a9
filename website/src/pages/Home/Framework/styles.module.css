/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

.cardContainer {
  display: grid;
  gap: 2rem;
  margin: 2rem auto;
  grid-template-columns: repeat(3, 1fr);
}

.card {
  border: 1px solid var(--home-border);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.03);
  background-color: var(--home-background);
  display: flex;
  flex-direction: column;
}

.cardContent {
  padding: 16px 24px;
}

.cardTitle {
  margin-top: 10px;
  margin-bottom: 5px;
  font-size: 20px;
  line-height: 140%;
}

.cardDescription {
  margin-top: 0;
  color: var(--home-secondary-text);
  font-size: 16px;
  line-height: 150%;
}

@media only screen and (max-width: 900px) {
  .cardContainer {
    display: flex;
    flex-direction: column;
  }

  .card {
    flex-direction: row;
    text-align: start;
  }

  .cardImage {
    width: auto;
    height: 150px;
    margin: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--home-border);
  }
}

@media only screen and (max-width: 650px) {
  .card {
    flex-direction: column;
    text-align: center;
    align-items: center;
  }

  .cardImage {
    margin-bottom: 0;
    max-width: calc(100% - 2rem);
  }
}
