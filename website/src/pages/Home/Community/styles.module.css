/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

.featureContainer {
  display: flex;
  justify-content: space-between;
  gap: 2rem;
  margin: 1rem auto;
}

.featureContainer > :first-child {
  border-right: 1px solid var(--home-border);
  padding-right: 2rem;
}

@media only screen and (max-width: 900px) {
  .featureContainer {
    flex-direction: column;
    gap: 0rem;
    max-width: 600px;
  }

  .featureContainer > :first-child {
    border-right: none;
    padding-right: 0;
  }
}
