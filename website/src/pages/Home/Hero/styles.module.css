/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

.container {
  height: 900px;
}

.backgroundContainer {
  position: absolute;
  z-index: -1;
  overflow: hidden;
  width: 100%;
}

.socialLinks {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
  margin-right: 1rem;
}

.gridBackground {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  height: 600px;
}

.devices {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: -100px;
  left: -20px;
}

.floorBackground {
  display: flex;
  justify-content: center;
  width: 100vw;
  height: 300px;
  position: relative;
  top: -4px;
  background:
    linear-gradient(
      to bottom,
      var(--home-hero-floor-background) 0%,
      var(--home-hero-floor-background-bottom)
    ),
    var(--home-hero-floor-background-bottom);
}

.svgContent {
  position: absolute;
}

.logo {
  margin-bottom: 1rem;
}

.title {
  font-size: 40px;
  margin-bottom: 0.5rem;
  margin-top: 1.5rem;
}

.subtitle {
  font-weight: 500;
  color: var(--home-secondary-text);
  font-size: 28px;
}

.content {
  position: relative;
  top: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  z-index: 1;
}

.buttonContainer {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.primaryButton {
  background-color: var(--home-button-primary);
  color: var(--home-button-primary-text);
  border: none;
  padding: 10px 24px;
  border-radius: 99rem;
  font-weight: bold;
  font-size: 17px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.35rem;
}

.primaryButton:hover {
  color: var(--home-button-primary-text);
  background-color: var(--home-button-primary-hover);
}

.secondaryButton {
  border: none;
  padding: 10px 24px;
  border-radius: 99rem;
  font-weight: bold;
  font-size: 17px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.35rem;
  color: var(--home-button-secondary-text);
  border: 1px solid var(--home-button-secondary-border);
  background-color: var(--home-button-secondary);
}

.secondaryButton:hover {
  background-color: var(--home-button-secondary-hover);
  color: var(--home-button-secondary-text);
}

@media (max-width: 600px) {
  .largeFormatDevices {
    display: none;
  }
}

@media (max-width: 450px) {
  .content {
    top: 50px;
  }

  .primaryButton,
  .secondaryButton {
    width: calc(100% - 2rem);
  }
}
