/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

import React from 'react';

import styles from './styles.module.css';

function Devices() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width={1107}
      height={288}
      fill="none"
      className={styles.svgContent}>
      <g filter="url(#a)" className={styles.largeFormatDevices}>
        <path
          fill="var(--home-hero-devices-background)"
          d="M772.117 21.578c0-8.542 6.925-15.466 15.466-15.466h287.667c8.54 0 15.47 6.924 15.47 15.466v208.015c0 8.542-6.93 15.466-15.47 15.466H787.583c-8.541 0-15.466-6.924-15.466-15.466V21.578Z"
        />
        <path
          stroke="var(--home-hero-devices-border)"
          strokeWidth={6.92}
          d="M787.583 9.572h287.667c6.63 0 12.01 5.375 12.01 12.006v208.015c0 6.631-5.38 12.006-12.01 12.006H787.583c-6.631 0-12.006-5.375-12.006-12.006V21.578c0-6.63 5.375-12.006 12.006-12.006Z"
        />
      </g>
      <g className={styles.largeFormatDevices}>
        <rect
          width={160.853}
          height={107.235}
          x={796.212}
          y={31.259}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <rect
          width={79.846}
          height={58.097}
          x={796.381}
          y={156.499}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <rect
          width={79.846}
          height={58.097}
          x={891.226}
          y={156.499}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <rect
          width={79.846}
          height={58.097}
          x={986.072}
          y={156.499}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <rect
          width={92.811}
          height={25.332}
          x={970.56}
          y={31.259}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <rect
          width={92.811}
          height={11.666}
          x={970.56}
          y={61.591}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={5.833}
        />
        <rect
          width={67.837}
          height={11.666}
          x={970.56}
          y={78.257}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={5.833}
        />
        <g filter="url(#b)">
          <rect
            width={60}
            height={60}
            x={833.416}
            y={220.526}
            fill="var(--home-hero-devices-background)"
            rx={15}
          />
          <rect
            width={59.5}
            height={59.5}
            x={833.666}
            y={220.776}
            stroke="var(--home-hero-devices-icon-border)"
            strokeWidth={0.5}
            rx={14.75}
          />
        </g>
        <g className={styles.largeFormatDevices}>
          <path
            fill="var(--home-hero-devices-background)"
            d="M863.416 259.427a8.905 8.905 0 1 0 0-17.81 8.905 8.905 0 0 0 0 17.81Z"
          />
          <path
            fill="url(#d)"
            d="M863.416 241.621h15.421a17.797 17.797 0 0 0-15.422-8.905 17.808 17.808 0 0 0-15.422 8.907l7.711 13.356.007-.002a8.901 8.901 0 0 1-.01-8.906 8.888 8.888 0 0 1 7.715-4.45Z"
          />
          <path
            fill="#1A73E8"
            d="M863.416 257.576a7.05 7.05 0 1 0 0-14.1 7.05 7.05 0 0 0 0 14.1Z"
          />
          <path
            fill="url(#e)"
            d="m871.127 254.981-7.711 13.355a17.8 17.8 0 0 0 15.423-8.904 17.807 17.807 0 0 0-.003-17.808h-15.422l-.001.006a8.898 8.898 0 0 1 8.911 8.898 8.902 8.902 0 0 1-1.197 4.453Z"
          />
          <path
            fill="url(#f)"
            d="m855.705 254.981-7.711-13.355a17.808 17.808 0 0 0-.001 17.809 17.805 17.805 0 0 0 15.425 8.901l7.71-13.355-.005-.005a8.888 8.888 0 0 1-7.707 4.462 8.895 8.895 0 0 1-7.711-4.457Z"
          />
        </g>
      </g>
      <g filter="url(#g)" className={styles.largeFormatDevices}>
        <rect
          width={60}
          height={60}
          x={901.416}
          y={220.526}
          fill="var(--home-hero-devices-background)"
          rx={15}
        />
        <rect
          width={59.5}
          height={59.5}
          x={901.666}
          y={220.776}
          stroke="var(--home-hero-devices-icon-border)"
          strokeWidth={0.5}
          rx={14.75}
        />
        <path
          fill="url(#h)"
          d="M948.781 243.158c-.814-1.959-2.466-4.074-3.76-4.743a19.443 19.443 0 0 1 1.898 5.688l.004.031c-2.119-5.282-5.712-7.412-8.646-12.049a23.04 23.04 0 0 1-.442-.718 5.954 5.954 0 0 1-.206-.387 3.41 3.41 0 0 1-.28-.742.047.047 0 0 0-.012-.032.045.045 0 0 0-.03-.016.056.056 0 0 0-.036 0l-.009.005-.013.007.007-.012c-4.707 2.756-6.304 7.858-6.451 10.41a9.379 9.379 0 0 0-5.157 1.987 5.724 5.724 0 0 0-.483-.366 8.681 8.681 0 0 1-.053-4.58 13.864 13.864 0 0 0-4.511 3.486h-.008c-.743-.942-.691-4.046-.648-4.694-.22.088-.43.2-.626.332-.655.468-1.268.993-1.831 1.569a16.37 16.37 0 0 0-1.752 2.102v.003-.004a15.825 15.825 0 0 0-2.514 5.678l-.025.124c-.07.389-.131.779-.185 1.17 0 .014-.003.027-.004.041a17.81 17.81 0 0 0-.304 2.583v.097a18.74 18.74 0 0 0 37.207 3.169c.032-.242.057-.481.085-.725a19.28 19.28 0 0 0-1.215-9.414Zm-21.598 14.669c.087.042.169.087.259.127l.013.009a9.848 9.848 0 0 1-.272-.136Zm19.741-13.688v-.018l.003.02-.003-.002Z"
        />
        <path
          fill="url(#i)"
          d="M948.781 243.158c-.815-1.959-2.466-4.074-3.76-4.743a19.443 19.443 0 0 1 1.898 5.688v.018l.003.02a16.965 16.965 0 0 1-.583 12.647c-2.147 4.609-7.346 9.332-15.484 9.102-8.792-.249-16.536-6.773-17.983-15.319-.264-1.347 0-2.03.132-3.126-.18.851-.281 1.717-.301 2.586v.097a18.74 18.74 0 0 0 37.207 3.169c.032-.242.058-.481.086-.726a19.265 19.265 0 0 0-1.216-9.413h.001Z"
        />
        <path
          fill="url(#j)"
          d="M948.781 243.158c-.815-1.959-2.466-4.074-3.76-4.743a19.443 19.443 0 0 1 1.898 5.688v.018l.003.02a16.965 16.965 0 0 1-.583 12.647c-2.147 4.609-7.346 9.332-15.484 9.102-8.792-.249-16.536-6.773-17.983-15.319-.264-1.347 0-2.03.132-3.126-.18.851-.281 1.717-.301 2.586v.097a18.74 18.74 0 0 0 37.207 3.169c.032-.242.058-.481.086-.726a19.265 19.265 0 0 0-1.216-9.413h.001Z"
        />
        <path
          fill="url(#k)"
          d="M939.676 245.361c.04.028.078.057.116.085a10.187 10.187 0 0 0-1.74-2.27c-5.825-5.825-1.527-12.631-.802-12.976l.007-.011c-4.706 2.756-6.304 7.858-6.45 10.41.218-.015.435-.033.658-.033a9.459 9.459 0 0 1 8.211 4.795Z"
        />
        <path
          fill="url(#l)"
          d="M931.477 246.524c-.031.466-1.678 2.074-2.254 2.074-5.328 0-6.193 3.223-6.193 3.223.235 2.715 2.127 4.951 4.413 6.133.105.054.211.103.317.151.183.081.366.156.55.225a8.311 8.311 0 0 0 2.438.471c9.34.438 11.149-11.169 4.409-14.537 1.59-.207 3.2.184 4.519 1.097a9.461 9.461 0 0 0-8.211-4.795c-.223 0-.44.018-.659.033a9.38 9.38 0 0 0-5.157 1.988c.286.242.608.565 1.288 1.234 1.271 1.253 4.533 2.551 4.54 2.703Z"
        />
        <path
          fill="url(#m)"
          d="M931.477 246.524c-.031.466-1.678 2.074-2.254 2.074-5.328 0-6.193 3.223-6.193 3.223.235 2.715 2.127 4.951 4.413 6.133.105.054.211.103.317.151.183.081.366.156.55.225a8.311 8.311 0 0 0 2.438.471c9.34.438 11.149-11.169 4.409-14.537 1.59-.207 3.2.184 4.519 1.097a9.461 9.461 0 0 0-8.211-4.795c-.223 0-.44.018-.659.033a9.38 9.38 0 0 0-5.157 1.988c.286.242.608.565 1.288 1.234 1.271 1.253 4.533 2.551 4.54 2.703Z"
        />
        <path
          fill="url(#n)"
          d="M924.776 241.964c.152.096.277.18.387.256a8.681 8.681 0 0 1-.053-4.58 13.891 13.891 0 0 0-4.511 3.486c.092-.002 2.81-.051 4.177.838Z"
        />
        <path
          fill="url(#o)"
          d="M912.876 250.571c1.446 8.545 9.191 15.07 17.983 15.319 8.138.23 13.336-4.494 15.484-9.102a16.972 16.972 0 0 0 .584-12.648v-.018c0-.014-.003-.022 0-.018l.003.032c.665 4.341-1.543 8.545-4.994 11.389l-.011.024c-6.724 5.477-13.16 3.304-14.462 2.417a8.623 8.623 0 0 1-.273-.135c-3.921-1.874-5.541-5.447-5.193-8.51a4.817 4.817 0 0 1-4.439-2.792 7.066 7.066 0 0 1 6.889-.277 9.33 9.33 0 0 0 7.036.277c-.007-.153-3.268-1.451-4.54-2.703-.679-.67-1.002-.992-1.287-1.235a5.74 5.74 0 0 0-.484-.366 24.102 24.102 0 0 0-.387-.257c-1.367-.888-4.085-.84-4.175-.837h-.008c-.743-.942-.691-4.046-.648-4.694-.22.088-.43.2-.626.332-.656.468-1.269.993-1.832 1.569a16.473 16.473 0 0 0-1.759 2.097v.003-.003a15.837 15.837 0 0 0-2.514 5.677c-.009.038-.675 2.949-.346 4.459h-.001Z"
        />
        <path
          fill="url(#p)"
          d="M938.053 243.177c.684.672 1.27 1.437 1.74 2.272.103.078.2.155.281.23 4.249 3.917 2.023 9.453 1.857 9.847 3.451-2.844 5.657-7.049 4.994-11.389-2.12-5.285-5.712-7.414-8.647-12.052a23.04 23.04 0 0 1-.442-.718 5.954 5.954 0 0 1-.206-.387 3.372 3.372 0 0 1-.28-.742.043.043 0 0 0-.012-.032.043.043 0 0 0-.03-.016.07.07 0 0 0-.035 0c-.003 0-.007.004-.01.005-.002.001-.009.006-.013.007-.725.344-5.022 7.149.803 12.975Z"
        />
        <path
          fill="url(#q)"
          d="M940.071 245.677a3.92 3.92 0 0 0-.281-.231c-.038-.028-.076-.057-.116-.085a6.469 6.469 0 0 0-4.519-1.097c6.74 3.37 4.932 14.975-4.409 14.537a8.324 8.324 0 0 1-2.438-.471 9.086 9.086 0 0 1-.866-.376l.013.009c1.302.889 7.736 3.061 14.462-2.418l.011-.024c.168-.392 2.394-5.929-1.857-9.844Z"
        />
        <path
          fill="url(#r)"
          d="M923.03 251.822s.865-3.224 6.194-3.224c.576 0 2.224-1.607 2.253-2.074a9.33 9.33 0 0 1-7.036-.276 7.072 7.072 0 0 0-6.889.276 4.812 4.812 0 0 0 4.439 2.793c-.347 3.064 1.272 6.636 5.193 8.509.088.042.17.088.26.128-2.289-1.182-4.178-3.418-4.414-6.132Z"
        />
        <path
          fill="url(#s)"
          d="M948.781 243.158c-.814-1.959-2.466-4.074-3.759-4.743a19.47 19.47 0 0 1 1.897 5.688l.004.031c-2.119-5.282-5.712-7.412-8.646-12.049a23.04 23.04 0 0 1-.442-.718 5.954 5.954 0 0 1-.206-.387 3.41 3.41 0 0 1-.28-.742.043.043 0 0 0-.012-.032.045.045 0 0 0-.03-.016.053.053 0 0 0-.035 0c-.003 0-.007.004-.01.005l-.013.007.007-.012c-4.707 2.756-6.304 7.858-6.451 10.41.219-.015.435-.034.659-.034a9.462 9.462 0 0 1 8.211 4.795 6.47 6.47 0 0 0-4.519-1.097c6.74 3.37 4.932 14.975-4.409 14.537a8.31 8.31 0 0 1-2.438-.47 10.25 10.25 0 0 1-.55-.225c-.106-.049-.212-.097-.317-.151l.013.008a11.489 11.489 0 0 1-.272-.136c.087.042.169.088.259.128-2.288-1.183-4.178-3.419-4.413-6.133 0 0 .865-3.224 6.193-3.224.576 0 2.225-1.607 2.254-2.074-.007-.152-3.268-1.45-4.54-2.702-.679-.67-1.002-.992-1.287-1.235a5.533 5.533 0 0 0-.484-.366 8.692 8.692 0 0 1-.053-4.58 13.87 13.87 0 0 0-4.51 3.486h-.009c-.742-.942-.69-4.046-.648-4.694a3.313 3.313 0 0 0-.625.332c-.656.468-1.269.993-1.832 1.569a16.396 16.396 0 0 0-1.752 2.102v.003-.004a15.842 15.842 0 0 0-2.514 5.678l-.025.124a33.189 33.189 0 0 0-.216 1.182 21.803 21.803 0 0 0-.277 2.612v.097a18.741 18.741 0 0 0 37.208 3.169c.031-.242.057-.481.085-.725a19.269 19.269 0 0 0-1.216-9.414Z"
        />
      </g>
      <g filter="url(#t)" className={styles.largeFormatDevices}>
        <rect
          width={60}
          height={60}
          x={969.416}
          y={220.526}
          fill="var(--home-hero-devices-background)"
          rx={15}
        />
        <rect
          width={59.5}
          height={59.5}
          x={969.666}
          y={220.776}
          stroke="var(--home-hero-devices-icon-border)"
          strokeWidth={0.5}
          rx={14.75}
        />
        <path
          fill="url(#u)"
          d="M999.416 268.075c9.694 0 17.544-7.857 17.544-17.549 0-9.691-7.85-17.548-17.544-17.548-9.692 0-17.548 7.857-17.548 17.548 0 9.692 7.856 17.549 17.548 17.549Z"
        />
        <path
          stroke="var(--home-hero-devices-background)"
          strokeLinecap="square"
          strokeWidth={0.338}
          d="M999.417 266.519v-2.694m0-26.61v-2.694m-2.786 31.764.468-2.654m4.621-26.204.47-2.654m-8.247 30.785.922-2.532m9.105-25.004.92-2.532m-13.486 28.902 1.347-2.334m13.309-23.044 1.34-2.333m-18.253 26.104 1.732-2.064m17.101-20.384 1.73-2.064m-22.567 22.533 2.064-1.732m20.383-17.104 2.06-1.732m-26.096 18.278 2.334-1.347m23.042-13.305 2.34-1.347M984.355 256l2.533-.921m25.002-9.101 2.53-.922m-30.77 8.266 2.654-.468m26.206-4.621 2.65-.468m-31.737 2.762h2.695m26.612 0h2.69m-31.763-2.768 2.654.468m26.209 4.621 2.65.468m-30.786-8.255 2.532.921m25.004 9.101 2.53.922m-28.902-13.459 2.334 1.347m23.048 13.305 2.33 1.347m-26.112-18.312 2.064 1.732m20.388 17.103 2.06 1.732m-22.547-22.525 1.732 2.064m17.105 20.384 1.73 2.064m-18.273-26.118 1.347 2.334m13.306 23.044 1.35 2.333m-13.466-28.89.922 2.533m9.104 25.004.92 2.532m-8.269-30.765.468 2.654m4.621 26.204.47 2.654m-4.166.155.117-1.343m2.559-29.191.11-1.343m-5.52 31.412.348-1.301m7.582-28.305.35-1.301m-10.915 29.927.57-1.221m12.385-26.558.57-1.221m-15.936 27.622.773-1.104m16.803-24.004.78-1.103m-20.478 24.414.953-.953m20.725-20.721.95-.952m-24.429 20.505 1.104-.773m24.005-16.808 1.1-.773m-27.6 15.927 1.221-.569m26.559-12.385 1.22-.569m-29.962 10.908 1.301-.348m28.301-7.585 1.31-.348m-31.407 5.528 1.342-.117m29.195-2.554 1.34-.118m-31.865 0 1.342.117m29.193 2.554 1.34.118m-31.412-5.524 1.301.349m28.301 7.584 1.31.349m-29.942-10.92 1.221.569m26.561 12.385 1.22.569m-27.605-15.921 1.104.773m24.001 16.808 1.11.772m-24.409-20.494.953.953m20.716 20.72.96.953m-20.523-24.432.773 1.104m16.81 24.004.77 1.104m-15.917-27.591.57 1.221m12.387 26.558.57 1.221m-10.916-29.948.349 1.301m7.587 28.305.35 1.301m-5.55-31.4.117 1.343m2.553 29.191.12 1.343"
        />
        <path
          fill="white"
          fillRule="evenodd"
          d="m1011.87 240.105-13.845 8.858-11.082 12.025 13.907-8.656 11.02-12.227Z"
          clipRule="evenodd"
        />
        <path
          fill="#FF3B30"
          fillRule="evenodd"
          d="m1011.87 240.105-13.845 8.858 2.825 3.369 11.02-12.227Z"
          clipRule="evenodd"
        />
      </g>
      <g filter="url(#v)">
        <rect
          width={115.27}
          height={241.645}
          x={607.87}
          y={4.173}
          fill="var(--home-hero-devices-background)"
          stroke="var(--home-hero-devices-border)"
          strokeWidth={6.92}
          rx={18.966}
        />
        <rect
          width={90.043}
          height={29.538}
          x={620.484}
          y={49.475}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <rect
          width={90.043}
          height={29.538}
          x={620.484}
          y={87.013}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <rect
          width={90.043}
          height={63.547}
          x={620.484}
          y={124.552}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <rect
          width={40.985}
          height={11.599}
          x={645.008}
          y={17.339}
          fill="var(--home-hero-devices-border)"
          stroke="var(--home-hero-devices-border)"
          strokeWidth={0.773}
          rx={5.8}
        />
      </g>
      <g filter="url(#w)">
        <rect
          width={60}
          height={60}
          x={635.5}
          y={221.051}
          fill="var(--home-hero-devices-background)"
          rx={15}
        />
        <rect
          width={59.5}
          height={59.5}
          x={635.75}
          y={221.301}
          stroke="var(--home-hero-devices-icon-border)"
          strokeWidth={0.5}
          rx={14.75}
        />
        <path
          fill="var(--home-hero-devices-icon)"
          d="M678.762 243.797c-.205.158-3.811 2.19-3.811 6.709 0 5.226 4.589 7.075 4.726 7.121-.021.113-.729 2.532-2.419 4.997-1.508 2.17-3.082 4.336-5.477 4.336-2.395 0-3.011-1.391-5.776-1.391-2.694 0-3.652 1.437-5.842 1.437-2.191 0-3.719-2.008-5.477-4.473-2.036-2.895-3.68-7.393-3.68-11.661 0-6.847 4.451-10.478 8.833-10.478 2.328 0 4.268 1.529 5.73 1.529 1.391 0 3.56-1.62 6.209-1.62 1.003 0 4.61.091 6.984 3.494Zm-8.242-6.393c1.096-1.299 1.871-3.102 1.871-4.906 0-.25-.022-.503-.067-.707-1.782.066-3.903 1.186-5.181 2.669-1.004 1.141-1.941 2.944-1.941 4.772 0 .275.046.55.067.638.113.021.296.045.479.045 1.599 0 3.61-1.07 4.772-2.511Z"
        />
      </g>
      <g filter="url(#x)">
        <rect
          width={124.957}
          height={240.962}
          x={425.347}
          y={5.105}
          fill="var(--home-hero-devices-background)"
          stroke="var(--home-hero-devices-border)"
          strokeWidth={6.92}
          rx={5.82}
        />
        <rect
          width={100.149}
          height={29.538}
          x={437.751}
          y={50.475}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <rect
          width={100.149}
          height={29.538}
          x={437.751}
          y={88.013}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <rect
          width={100.149}
          height={63.547}
          x={437.751}
          y={125.552}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <circle
          cx={487.862}
          cy={23.139}
          r={5.8}
          fill="var(--home-hero-devices-border)"
          stroke="var(--home-hero-devices-border)"
          strokeWidth={0.773}
        />
      </g>
      <g filter="url(#y)">
        <rect
          width={60}
          height={60}
          x={457.862}
          y={222.083}
          fill="var(--home-hero-devices-background)"
          rx={15}
        />
        <rect
          width={59.5}
          height={59.5}
          x={458.112}
          y={222.333}
          stroke="var(--home-hero-devices-icon-border)"
          strokeWidth={0.5}
          rx={14.75}
        />
        <path
          fill="var(--home-hero-devices-icon)"
          d="M471.783 262.526c-1.285 0-2.606 1.032-2.606 2.836 0 1.655 1.162 2.819 2.606 2.819 1.193 0 1.724-.803 1.724-.803v.348a.37.37 0 0 0 .349.35h.861v-5.432h-1.21v.69s-.536-.808-1.724-.808Zm.216 1.111c1.057 0 1.613.932 1.613 1.728 0 .886-.659 1.727-1.611 1.727-.795 0-1.592-.645-1.592-1.739 0-.99.685-1.719 1.59-1.719v.003Zm4.401 4.443a.347.347 0 0 1-.249-.099.34.34 0 0 1-.1-.251v-5.086h1.212v.673c.274-.414.809-.797 1.632-.797 1.345 0 2.063 1.074 2.063 2.079v3.481h-.843a.375.375 0 0 1-.37-.37v-2.842c0-.556-.34-1.233-1.127-1.233-.85 0-1.356.805-1.356 1.562v2.883h-.862Zm8.086-5.554c-1.285 0-2.607 1.032-2.607 2.836 0 1.655 1.162 2.819 2.607 2.819 1.195 0 1.725-.803 1.725-.803v.348a.37.37 0 0 0 .349.35h.862v-8.144h-1.211v3.404s-.537-.81-1.725-.81Zm.216 1.111c1.057 0 1.612.932 1.612 1.728 0 .886-.659 1.727-1.611 1.727-.795 0-1.591-.645-1.591-1.739-.001-.99.683-1.719 1.59-1.719v.003Zm4.401 4.443a.347.347 0 0 1-.249-.099.34.34 0 0 1-.099-.251v-5.086h1.21v.905c.209-.507.658-.966 1.458-.966.144.001.288.015.43.041v1.255a1.702 1.702 0 0 0-.574-.103c-.85 0-1.314.804-1.314 1.563v2.741h-.862Zm10.097 0a.337.337 0 0 1-.349-.35v-5.086h1.212v5.436h-.863Zm4.412-5.554c-1.285 0-2.606 1.032-2.606 2.836 0 1.655 1.162 2.819 2.606 2.819 1.192 0 1.724-.803 1.724-.803v.348a.37.37 0 0 0 .349.35h.862v-8.144h-1.211v3.404s-.536-.81-1.724-.81Zm.216 1.111c1.06 0 1.612.932 1.612 1.728 0 .886-.659 1.727-1.61 1.727-.796 0-1.591-.645-1.591-1.739-.001-.99.684-1.719 1.589-1.719v.003Zm-4.381-2.162a.802.802 0 1 0-.002-1.604.802.802 0 0 0 .002 1.604Zm-4.401 1.046c-1.345 0-2.823 1.009-2.823 2.832 0 1.662 1.259 2.828 2.821 2.828 1.925 0 2.865-1.551 2.865-2.818a2.817 2.817 0 0 0-2.862-2.842h-.001Zm.004 1.134c.931 0 1.625.752 1.625 1.701 0 .966-.736 1.713-1.622 1.713-.821 0-1.62-.67-1.62-1.694 0-1.042.76-1.718 1.617-1.718v-.002Z"
        />
        <path
          fill="#32DE84"
          d="m496.982 241.807 3.098-5.379a.631.631 0 0 0-.702-.925.623.623 0 0 0-.38.293l-3.138 5.449c-2.394-1.097-5.089-1.708-7.988-1.708s-5.594.611-7.989 1.708l-3.138-5.449a.628.628 0 1 0-1.084.632l3.097 5.379c-5.342 2.902-8.959 8.322-9.557 14.667h37.346c-.6-6.345-4.219-11.765-9.565-14.667Zm-17.683 9.41a1.57 1.57 0 1 1 .599-3.019 1.57 1.57 0 0 1-.6 3.019h.001Zm17.144 0a1.565 1.565 0 0 1-1.536-1.876 1.574 1.574 0 0 1 1.231-1.233 1.564 1.564 0 0 1 1.607.668 1.573 1.573 0 0 1-1.303 2.441h.001Z"
        />
      </g>
      <g
        stroke="var(--home-hero-devices-border)"
        filter="url(#z)"
        className={styles.largeFormatDevices}>
        <path
          fill="var(--home-hero-devices-background)"
          strokeWidth={6.918}
          d="M72.117 11.34h255.95c6.112 0 11.068 4.955 11.068 11.068v167.405H61.049V22.408c0-6.113 4.955-11.068 11.068-11.068Z"
        />
        <path
          fill="url(#A)"
          strokeWidth={0.692}
          d="m17.814 233.048 40.122-40.122h284.658l40.121 40.122H17.814Z"
        />
        <path
          fill="var(--home-hero-devices-border)"
          strokeWidth={0.692}
          d="M17.346 233.729h365.849v8.647a5.88 5.88 0 0 1-5.88 5.88H23.225a5.88 5.88 0 0 1-5.88-5.88v-8.647Z"
        />
      </g>
      <g className={styles.largeFormatDevices}>
        <rect
          width={156.853}
          height={103.235}
          x={84.838}
          y={61.281}
          fill="var(--home-hero-devices-background)"
          stroke="var(--home-hero-devices-skeleton-element)"
          strokeWidth={4}
          rx={4}
        />
        <rect
          width={160.853}
          height={107.235}
          x={149.265}
          y={35.259}
          fill="var(--home-hero-devices-skeleton-element)"
          rx={6}
        />
        <g filter="url(#B)">
          <rect
            width={60}
            height={60}
            x={209.521}
            y={222.826}
            fill="var(--home-hero-devices-background)"
            rx={15}
          />
          <rect
            width={59.5}
            height={59.5}
            x={209.771}
            y={223.076}
            stroke="var(--home-hero-devices-icon-border)"
            strokeWidth={0.5}
            rx={14.75}
          />
          <path fill="#E15F38" d="M222.961 236.265h15.786v15.786h-15.786z" />
          <path fill="#8EBB3B" d="M240.296 236.265h15.786v15.786h-15.786z" />
          <path fill="#F5BD45" d="M240.296 253.601h15.786v15.786h-15.786z" />
          <path fill="#4BA4EA" d="M222.961 253.601h15.786v15.786h-15.786z" />
        </g>
        <g filter="url(#C)">
          <rect
            width={60.513}
            height={60.513}
            x={141.008}
            y={222.826}
            fill="var(--home-hero-devices-background)"
            rx={15}
            shapeRendering="crispEdges"
          />
          <rect
            width={59.513}
            height={59.513}
            x={141.508}
            y={223.326}
            stroke="var(--home-hero-devices-icon-border)"
            rx={14.5}
            shapeRendering="crispEdges"
          />
          <path fill="url(#D)" d="M149.008 230.826h44.513v44.513h-44.513z" />
        </g>
      </g>
      <defs>
        <filter
          id="a"
          width={350.598}
          height={270.947}
          x={756.117}
          y={6.112}
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse">
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feMorphology
            in="SourceAlpha"
            radius={16}
            result="effect1_dropShadow_490_198"
          />
          <feOffset dy={16} />
          <feGaussianBlur stdDeviation={16} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_490_198"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect1_dropShadow_490_198"
            result="shape"
          />
        </filter>
        <filter
          id="b"
          width={66}
          height={66}
          x={830.416}
          y={218.526}
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse">
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1.5} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.025 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_490_198"
          />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
          <feBlend
            in2="effect1_dropShadow_490_198"
            result="effect2_dropShadow_490_198"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect2_dropShadow_490_198"
            result="shape"
          />
        </filter>
        <filter
          id="g"
          width={66}
          height={66}
          x={898.416}
          y={218.526}
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse">
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1.5} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.025 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_490_198"
          />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
          <feBlend
            in2="effect1_dropShadow_490_198"
            result="effect2_dropShadow_490_198"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect2_dropShadow_490_198"
            result="shape"
          />
        </filter>
        <filter
          id="t"
          width={66}
          height={66}
          x={966.416}
          y={218.526}
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse">
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1.5} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.025 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_490_198"
          />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
          <feBlend
            in2="effect1_dropShadow_490_198"
            result="effect2_dropShadow_490_198"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect2_dropShadow_490_198"
            result="shape"
          />
        </filter>
        <filter
          id="v"
          width={154.19}
          height={280.565}
          x={588.41}
          y={0.713}
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse">
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feMorphology
            in="SourceAlpha"
            radius={16}
            result="effect1_dropShadow_490_198"
          />
          <feOffset dy={16} />
          <feGaussianBlur stdDeviation={16} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_490_198"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect1_dropShadow_490_198"
            result="shape"
          />
        </filter>
        <filter
          id="w"
          width={66}
          height={66}
          x={632.5}
          y={219.051}
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse">
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1.5} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.025 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_490_198"
          />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
          <feBlend
            in2="effect1_dropShadow_490_198"
            result="effect2_dropShadow_490_198"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect2_dropShadow_490_198"
            result="shape"
          />
        </filter>
        <filter
          id="x"
          width={163.877}
          height={279.882}
          x={405.887}
          y={1.645}
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse">
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feMorphology
            in="SourceAlpha"
            radius={16}
            result="effect1_dropShadow_490_198"
          />
          <feOffset dy={16} />
          <feGaussianBlur stdDeviation={16} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_490_198"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect1_dropShadow_490_198"
            result="shape"
          />
        </filter>
        <filter
          id="y"
          width={66}
          height={66}
          x={454.862}
          y={220.083}
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse">
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1.5} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.025 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_490_198"
          />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
          <feBlend
            in2="effect1_dropShadow_490_198"
            result="effect2_dropShadow_490_198"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect2_dropShadow_490_198"
            result="shape"
          />
        </filter>
        <filter
          id="z"
          width={398.572}
          height={272.721}
          x={0.979}
          y={7.881}
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse">
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feMorphology
            in="SourceAlpha"
            radius={16}
            result="effect1_dropShadow_490_198"
          />
          <feOffset dy={16} />
          <feGaussianBlur stdDeviation={16} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_490_198"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect1_dropShadow_490_198"
            result="shape"
          />
        </filter>
        <filter
          id="B"
          width={66}
          height={66}
          x={206.521}
          y={220.826}
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse">
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1.5} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.025 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_490_198"
          />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
          <feBlend
            in2="effect1_dropShadow_490_198"
            result="effect2_dropShadow_490_198"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect2_dropShadow_490_198"
            result="shape"
          />
        </filter>
        <filter
          id="C"
          width={66.513}
          height={66.513}
          x={138.008}
          y={220.826}
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse">
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1.5} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.025 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_490_198"
          />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy={1} />
          <feGaussianBlur stdDeviation={1} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
          <feBlend
            in2="effect1_dropShadow_490_198"
            result="effect2_dropShadow_490_198"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect2_dropShadow_490_198"
            result="shape"
          />
        </filter>
        <radialGradient
          id="i"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="matrix(39.0656 0 0 39.066 944.864 234.478)"
          gradientUnits="userSpaceOnUse">
          <stop offset={0.129} stopColor="#FFBD4F" />
          <stop offset={0.186} stopColor="#FFAC31" />
          <stop offset={0.247} stopColor="#FF9D17" />
          <stop offset={0.283} stopColor="#FF980E" />
          <stop offset={0.403} stopColor="#FF563B" />
          <stop offset={0.467} stopColor="#FF3750" />
          <stop offset={0.71} stopColor="#F5156C" />
          <stop offset={0.782} stopColor="#EB0878" />
          <stop offset={0.86} stopColor="#E50080" />
        </radialGradient>
        <radialGradient
          id="j"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="matrix(39.0656 0 0 39.066 930.589 250.449)"
          gradientUnits="userSpaceOnUse">
          <stop offset={0.3} stopColor="#960E18" />
          <stop offset={0.351} stopColor="#B11927" stopOpacity={0.74} />
          <stop offset={0.435} stopColor="#DB293D" stopOpacity={0.343} />
          <stop offset={0.497} stopColor="#F5334B" stopOpacity={0.094} />
          <stop offset={0.53} stopColor="#FF3750" stopOpacity={0} />
        </radialGradient>
        <radialGradient
          id="k"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="translate(935.299 225.613) scale(28.3015)"
          gradientUnits="userSpaceOnUse">
          <stop offset={0.132} stopColor="#FFF44F" />
          <stop offset={0.252} stopColor="#FFDC3E" />
          <stop offset={0.506} stopColor="#FF9D12" />
          <stop offset={0.526} stopColor="#FF980E" />
        </radialGradient>
        <radialGradient
          id="l"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="translate(926.268 260.653) scale(18.6011)"
          gradientUnits="userSpaceOnUse">
          <stop offset={0.353} stopColor="#3A8EE6" />
          <stop offset={0.472} stopColor="#5C79F0" />
          <stop offset={0.669} stopColor="#9059FF" />
          <stop offset={1} stopColor="#C139E6" />
        </radialGradient>
        <radialGradient
          id="m"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="matrix(9.58605 -2.3177 2.7134 11.22274 932.724 247.248)"
          gradientUnits="userSpaceOnUse">
          <stop offset={0.206} stopColor="#9059FF" stopOpacity={0} />
          <stop offset={0.278} stopColor="#8C4FF3" stopOpacity={0.064} />
          <stop offset={0.747} stopColor="#7716A8" stopOpacity={0.45} />
          <stop offset={0.975} stopColor="#6E008B" stopOpacity={0.6} />
        </radialGradient>
        <radialGradient
          id="n"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="translate(930.096 232.88) scale(13.3813)"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#FFE226" />
          <stop offset={0.121} stopColor="#FFDB27" />
          <stop offset={0.295} stopColor="#FFC82A" />
          <stop offset={0.502} stopColor="#FFA930" />
          <stop offset={0.732} stopColor="#FF7E37" />
          <stop offset={0.792} stopColor="#FF7139" />
        </radialGradient>
        <radialGradient
          id="o"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="translate(940.614 224.392) scale(57.0915)"
          gradientUnits="userSpaceOnUse">
          <stop offset={0.113} stopColor="#FFF44F" />
          <stop offset={0.456} stopColor="#FF980E" />
          <stop offset={0.622} stopColor="#FF5634" />
          <stop offset={0.716} stopColor="#FF3647" />
          <stop offset={0.904} stopColor="#E31587" />
        </radialGradient>
        <radialGradient
          id="p"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="rotate(83.976 341.478 633.66) scale(41.8447 27.4623)"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#FFF44F" />
          <stop offset={0.06} stopColor="#FFE847" />
          <stop offset={0.168} stopColor="#FFC830" />
          <stop offset={0.304} stopColor="#FF980E" />
          <stop offset={0.356} stopColor="#FF8B16" />
          <stop offset={0.455} stopColor="#FF672A" />
          <stop offset={0.57} stopColor="#FF3647" />
          <stop offset={0.737} stopColor="#E31587" />
        </radialGradient>
        <radialGradient
          id="q"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="translate(929.781 237.836) scale(35.644)"
          gradientUnits="userSpaceOnUse">
          <stop offset={0.137} stopColor="#FFF44F" />
          <stop offset={0.48} stopColor="#FF980E" />
          <stop offset={0.592} stopColor="#FF5634" />
          <stop offset={0.655} stopColor="#FF3647" />
          <stop offset={0.904} stopColor="#E31587" />
        </radialGradient>
        <radialGradient
          id="r"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="translate(939.19 239.922) scale(39.0122)"
          gradientUnits="userSpaceOnUse">
          <stop offset={0.094} stopColor="#FFF44F" />
          <stop offset={0.231} stopColor="#FFE141" />
          <stop offset={0.509} stopColor="#FFAF1E" />
          <stop offset={0.626} stopColor="#FF980E" />
        </radialGradient>
        <linearGradient
          id="d"
          x1={847.993}
          x2={878.837}
          y1={243.847}
          y2={243.847}
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#D93025" />
          <stop offset={1} stopColor="#EA4335" />
        </linearGradient>
        <linearGradient
          id="e"
          x1={860.983}
          x2={876.405}
          y1={268.098}
          y2={241.387}
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#FCC934" />
          <stop offset={1} stopColor="#FBBC04" />
        </linearGradient>
        <linearGradient
          id="f"
          x1={865.344}
          x2={849.922}
          y1={267.223}
          y2={240.513}
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#1E8E3E" />
          <stop offset={1} stopColor="#34A853" />
        </linearGradient>
        <linearGradient
          id="h"
          x1={946.302}
          x2={915.194}
          y1={236.177}
          y2={266.191}
          gradientUnits="userSpaceOnUse">
          <stop offset={0.048} stopColor="#FFF44F" />
          <stop offset={0.111} stopColor="#FFE847" />
          <stop offset={0.225} stopColor="#FFC830" />
          <stop offset={0.368} stopColor="#FF980E" />
          <stop offset={0.401} stopColor="#FF8B16" />
          <stop offset={0.462} stopColor="#FF672A" />
          <stop offset={0.534} stopColor="#FF3647" />
          <stop offset={0.705} stopColor="#E31587" />
        </linearGradient>
        <linearGradient
          id="s"
          x1={945.928}
          x2={919.458}
          y1={236.017}
          y2={262.486}
          gradientUnits="userSpaceOnUse">
          <stop offset={0.167} stopColor="#FFF44F" stopOpacity={0.8} />
          <stop offset={0.266} stopColor="#FFF44F" stopOpacity={0.634} />
          <stop offset={0.489} stopColor="#FFF44F" stopOpacity={0.217} />
          <stop offset={0.6} stopColor="#FFF44F" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="u"
          x1={999.416}
          x2={999.416}
          y1={268.079}
          y2={232.975}
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#1E6FF1" />
          <stop offset={1} stopColor="#28CEFB" />
        </linearGradient>
        <linearGradient
          id="A"
          x1={200.265}
          x2={200.265}
          y1={192.926}
          y2={233.048}
          gradientUnits="userSpaceOnUse">
          <stop stopColor="var(--home-hero-devices-border)" />
          <stop offset={1} stopColor="var(--home-hero-devices-stop)" />
        </linearGradient>
        <pattern
          id="D"
          width={1}
          height={1}
          patternContentUnits="objectBoundingBox">
          <use xlinkHref="#E" transform="scale(.00172)" />
        </pattern>
        <image
          xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAkUAAAJFCAMAAADkoHPzAAAC91BMVEVHcEzv7+/w8PDw8PDv7+/q6ur4+Pj7+/v5+fn6+vr5+fn39/f6+vr6+vr7+/vw8PDm5ubl5eXk5OTl5eXk5OTl5eXl5eXk5OTk5OT39/f7+/v9/f3////z8/Ph4v3Nzv26wf+psv+kq/+fpv+Vov+LnP+Hmf+Ekf+Ajf98iv95iP92hv9vgf9sfv9mff9fef9yhP96jv+Elv+QoP+MlP+Bkf+apP+Tl/+Kof+goP+cl/+knP+rlv+mlvm1lP3Ak/Tw7/C6kOLAkNu0keiwku2rlPO/g9C0f9jHjM7s7O3m5ufOjsfh4eHGhcf2z9TVkMDckrnilrTfjq/k5OTp6ernkqrxeIf6aHXohJ3/XFv/XGD/XGb/lIbnjaT/t63/YVv/XVj+UkX/aFjOh77/ZFj/bVf9W0moed/WibWfduaVdO38Z07/fVaLcvX/dFZ/cf10cP/Ae8S2ec6sdthpc/9saf/4TkHLfbihbdnQZVCLqIEjWyZsbDWTaz+8aErjYFTP3bZCf1MATRQAWSIAWSQ3WyhZazF+bDqlv5cAWiUAViBKay2nakXfhaZxnGjVgK4DYCVLWykdZyWbbeUOYiS+0KUiaiTY4M14Z/sAXSSDaPQXZSS3csTuXkSvb8mNaeo+aijtUD6obtLCdLjgUzzRVTnReKlfWyrxXVrIdbDAVjXae6O5cbrhfp1zWiycWTCIWi6vVzKUad8obCQ2dCR3h78oaB4tbSF/mzoxcSODryWLtimMviWOqjO304Q7eCSmy19mizs/eySTtVuueINFfyRPhyVBgihKgySKeGl9qyZ3pyN1oiFUiyRmmSNwnyNiliNZjyNrmiNpnSVtoSVekyRypCQzaSVFkCB5qyR9sCSFtSaBsySHuiWQuyyTwjKUwCqRwyeZxC2XySWayjWeyy2bziSg0Sim1S+g0DWo3Ceq2zSk1ies4Cmv4DHM5pTE7Ga05jWv4Tmy5Sil1ze56jm36ym/8Dq77zDC9TO98yjC+CnF/CkXSuijAAAAGXRSTlMAFiEtO0xSY296jZ2uyeEKY4/Z67LDfHKhyeBcMwAAgABJREFUeNrsnXeAlcXV/wXERrPExF1EpXeko6KGmEQDwVjAKBgDUTREDTasWMgLFuxRELtGjdGoKcIPhN2IYkBfevelL72TUAX0j9/MKTPnzMxz9y7FuvPcvffuwqKyH7/ne8rMc9BB5at8la/yVb7KV/kqX+WrfJWv8lW+ylf5Kl/lq3yVr/JVvspX+Spf5at8la/yVb7KV/kqX+WrfJWv8lW+ylf5+hJWhYqVDq58yKGHHX74EUccUaVKVbWqVDFfPPzwww49pPLBlSpW+Kr/XcvX12pVqIjkADbVqlWrXr16QUFhzeMTq2ZhQYH5ZfObACokqhyo7/KqYYTnkEMOPfwIw071guP3ahVUNzwdcfihhxxi5KnGV/1fVL6+xFWjQkUL0OFVqlbbS3gSOFWrWuVwi1LFCuUwfdtXjRoVKlQ65PCq1Qr3Fz56FVarevghlSpUqFGO0rd3VTr0iGrVCwtr1qy578Ckl/mjCwurVzvi0Epf9X9r+ToAywBU1RjjwgPGj2Kp0Pyjqpaj9G1aNoTtvYHe+wXW2wS4r/q/v3zt46pw8KGHVzFB7MsGyC8T3qocfujB5dWAb+iqcPAhh1WpWv2rA8iv6lWrHHZIOUnfsGVy+UqHHFHtq4ZHr2pHHFKpvA7wTVkmmz/kiKpfYRDLXoWGpPIqwDdhVTq0qsnEvpRUrOyrpsneqpanbl/vVeOQw6tV/yq9dD6rsHr1aocfUi5IX89V8RBbEfqqGclv2WrSIRW/6r+x8hUsaGvscxgrLKh1gl0nwjopXPhl+xtqFeyz4NWsVl5L+jqtGhUPPmwfMnqPzkkn1a5dp269+g0aNmrcpGmz5i3MOhmXeW3RvFnLpq1ata7fpl7dOrWBqX0FqnrVww4uHwX4GqwaFSodWqV6zTIs1oLCwgLLj4Gndp16bRsbaJq71Uyt5s3oyy1aCLDMataqQb06hieEyXdX8v5XqVmzepVDK5Wn/1/tMkl91er488jrcj/iwsITDD116zdqaVfTlk15tZRLsySWYYmQQqaatWtT28BUAJ1egimPfx8Cqeoh5Rx9hevgw6sVFOKPIn8Vqmn5qdewXaNGjRs3adLUPPxqCRg1zSTJo9SClcktI2AtW7c3LNXMW5CAJDtBWe3wg7/qv8vv6Kp4qEnJ6H/n/FTo+AIDkPE9bY3vAWiawGoakBRg1FLHtmZSkOQC+9TCWCerSydw7zcPLQI9ql710PKc7cteNYwMVXc/g5z/33uArP9p1MRAYvFp3EStPDEyDDFIVo1SJFmYmrar396jVJoW4apuBKk8sH2Jq9IhVaoVOIN6fM3cElR4giWofdvGwE9jXoohhVHkkMKgxh+xILU42V64THyzZqkwW5Nq6n/5gmpVynP/L2nVqESOmn4QWVpEaTy4IAuQ5yfFUZOAo5YapGYSJZSjZghRDFKLk0/2eVyrerVNCleQ1qTj9b88Ou1K5YJ0wFeNSodV8wQ5hhIiZFN5o0J1GhqAGvnVuHEjR5F9ZHHUkr122h9ZjFowSoqkk/0rFwTqnWRAwkJA2hfxp5a0aoeVc3SAV6XDnaEuTYsKTjipNqx6jRoTQHA1ylSkpiFHYbbWMg5swFBokCCsycKSCW9tTjohDy1Ch3R4eVw7gKvy4dULxf+/WVpkrdCJhJBddTo0bsxKBI9GjRtlRbbYIWVm/VSObJb0RyRGHqUWLZq1q31igdKjUIv4q4XVD6/8Vf9df0tXjUOqVK+u/q9NaJFLx2rXsYs5cnKEGDXOhVGkSE21JrEkNVdGu0WLhD9CkLxLat60XfuTTvAWKUOLrBxVr1Le9t//q0LlKqRDxye0yJshyMfq1K1b1zJkL1x12zqOnCIpg6StdtMmOSKbhaglcdSsWfPsChJZpJOl2z65ZYM6J0FhMumL3H+J0aMqlctnbPfrqli5SjX5v2usRS6lr9O+PSCEq7aXo4ahPbIQNcpK2aKELUc5u5kvaIeK5OVIgNSiQe2TsACQpUX45WpVKpdXIvfbsjrk9T+pRQahWrYw3cEgZIWorodIyJFcjUN3lC1HLZvmytdcRbtFM/OIMQJFOlkL0sknt7ZtN4ptsRa5kna5Hu2nVePgI3xeltIiGEQ1CLUnhBAip0fM0Um12zTUGIEWZapRwFFpab8FyUDULApszFELjdHJJzc46QSYKsnUIjBIR5QXtPfD8rl9lhZZhk6qywQRRh4i77Fr12kbqFHCaSuWlNOm7r8sRUqOmqMmtcDUPxHYWpwcctTilBMLcmnR8eV5/35ZFQ6vXuA8aEqLzP/LJ5xUu27d9sYOndr+1LqnxhyJXM24o8ZRVGskJamJarE1TST+TcNkTRltCm6pHltKkFq0qnOiSNkiLbLPBdUPLw9r+7BqQMu1ZqYW2bTejgmdahgChACjU11Yq8vmyHNUp0PjRsFqjCVtL0WNszu1qYStZdDvN96oWYuEHBFDoR61qg/Jf4YWwZvq1Q4vD2t7uSocKifQIi2i4mKd00471a725mGi2qk2snmO4lwtliMX1rIsUtOmGqRwySKka7K1ULNs6X4tg9Qay5FejKQWgd5Wr3pouR7txapxcJXqmhutRccfb5Ky2qd1QIZoWYyQoRAkIUd1OzSM1CinO4qDWjw54iySTPybpQAKC0jYtK1z4gmirK21CP5rq1cpt9llXTUqHV5NF+W0Ftna0EmnaoQAIyNJdZmjOhkY2dpRo0ZJlCRGOUFSFLWM8v4cJe1Ekw1WG6whpbQI31U7vLxNW6ZV4RDBUEKLCgtOrN3BhrIYo1MtRnWlzRYY1dHJWpD556whwWykBilrlq05C5LN+9PlbMIpECTDUc3jE1rEsa5a+YB2GdbBVQsFQ7EW1TqxDhJEj/b24eWovXdHdWS2JmuQ9ZChhiFGUT07q+ffsmnTljln/Zu5qBZk/j7tj7x2y9on6N6g1CJbFqtaPp+d56pwWLVCyZDWIpvZ1+nY8bSOHU+VJEV6JNxR0mXX6RBx5NUo8NmNM2x2GNusPWqp+2vNmrdokaPDFspRi5btT5QJm9aimscXVjus3GXnsw7l7D6hRbbdahg6zTDU0aLTkQmKIpuPanVUVAuStdAcQcqvZ9lwmk1WtJvk8EhhxuaGkFoEGRuZo5ikpvV8BSnUIvNRvdqhX/VP6Ou/Djmiuvw7C3L7whNOqn0aMGTE6FT7YuPZaQARBrX2nKzZ2pFQI1GD9J21DqBFMUnRLFtmayRO2Fo2a+k7/h6jiCI5gRRwZBK2guNlYJOFesPREYd81T+lr/eq6DOzWIvs4FAdRshAZIMaKFLCZbfXFlubIy9H7dtmcBT67GyKYj1qporaWo3SCVsU2FrWJo5qRlqE2Vp5rz9zVahUpbBmxoISI4WyjvgMLxDZ4HHaqe3juEa17Dq+kq1ytdp1bIe2YaOMAlK+HLXUiqQbtc14lK1ZnPj7XC1qjdQ+oSB7D1JhlUrl9ii9KhyWY799Ya0TmRoGCR6nndqxY1KNnMOO+/xajixGZsUFJGKoUQqkGKO0ORKdEeiKJFv+sRYRR4XZfx/Vy112clWsmuMvrcAwdHrHjqfjU0ehRdYfnQoocdZ/qsv629d1ahS3+lWy1jAR1hoLlPLI+xOBzfdpQY1471EyX0sEtpa1a+X436pqeVSL16HVsyAywezE2qfbdcbpp1twTidVcpJkXXZHBCkWJGmO0p01O7/WsGGKpMbYZ8scHIna/elNIziBpBO2sISUimstWp2UHdYKq5cna8GqmHl6jGXoJATIXPbpjI7wRCRRWDsNnPZpqcAmYxtNZIdyVA/dUUO0SA2lGqU6bJqkIGPLctpqQDtDj6IWW7PWJxVmclS9SrkcyXVIVjSDApFFiBbFNFCk01RcsxxBASkEydUfT1VqVFvZo/YdyB1lxLWco2w5ErZmvlPrnHaLZum4dnJydKRF+xMzOSqsWp7zu0XjjGmGTjzz9NMFRPiMIJ3OWkQuG0pICSVqX1fEtYzxNXsQBIa10Gk35riWVUJqmlOO4r5IM1aj5um5kTCutax9QiZH5YOQtGocXDUrmNWsdYJkSIF0GnHknBFZo47JQraqZacatCcZd2SlqCGFtUQpUhikUgbZmmafgcS1yOQRSDzLFq5WJ51QMyusVS0fGDGrxqGZ+X2hCWZmnRlx1JFezujY0ZsjdkfJxL9u+1PlzFHdjJYIeWxGqRSn3STfuKYzNjGClBzRTiVs9U/MTGCrH1qOUcXMQuPxBSeeeeaZwFAkSIAQxLXTuYQkcDo1Y/lqdh29Zc0NjHRo6JK1eAQp9tk597Dl7Pi78ezmyQZb3BdpUadWZrL2nTfZB1ctyIpmJ/7wTFiWoTMTIEFwO+0Mim0i8z8V45tWo7rYoVVFyGh6TST9WVGtcbY/SghS1GHTW49Ikpql7FGoRy3anVQzwx0VfMfnRQ6tlmWrTzjpTF4kR2eGBHXk5N+uM4QenUoV7Sjdz6hAyqiGLRGObFqRMtI1NcsW7/FXELUMrbYwSM0DhsKidkvb7E//fX2X+/y0XTHBUIEXojPzUKTTO57eUQc1NNodc0W1rE1rdTmsYbKWTyFSYRRFtlTi39x7pKTRPjlxVoRx2bVPyOCo+hHfVTmqnJGb2b7rmXoBTKcjSQlBcjUkjxDNjXQM1IgIOrWu7tAqOapdr21DUcvOqGjnMEhNs512s2axQ8pViwwNUr0TCzPkqOp38qwaGq1OhXkQoh/CIwTpzNMz9KijBuk0IunUjnFcE01a1RHRYc3JUbvUoH9CjbL2QraMN2e7MmQz1/CnM9tTDX8lSC2zkv7jqx3y3evP1jg8w1ab9P6HPzwTr1CQTj8zjmsdKaZ1lBx1FEWkjEp21FgTQ9m1oXjk3VFSjxrlaow0VcP+LRN7RlpG9ogOjIjH2PRZEQ2yWv0F37mtjwdXS/9VHF944g8tRADSmT8MUcqIbEFNW9jsUzsm5EgLUp3UGCTsNgKK2nFoS3CUHdia5pGxyb5I8/A2EWFYE4Gt2UkF6ahWWO27ZY4OyWjgH9/JCNEPkSP7iOIaCVIc2DqKfE0K0qkpNSJJSoxlBy1apqghC1JyTjs56Z/cDJnrHG2/+SiersXgxhy1aJNhsgurf5f6ahnlamOrfygWBrU4shFLqZSto/NIQTEyzvlVyl83UYK087TOHtEgW7tE+Sg77U+gFA6zteQapBtCskcglWq0252UYbK/O9MiFdLN1+OP73SiREi4ozisnZ7O2Bghn/qfCiO1Uc5PCf+pCY50tqaKRw3TgS1ySJkt/8RMZEtXzKYZJHfoSKrH5uQoK+f/rpwvUvGIpK8+Hmx1tM70V2CPyGgn9ChhsrFNK0IaqZEbGMnO1vRK7xjJmfnnG9Wau421bJCCY7TVzuz6J6bdUcER34F+SI1KGVWighOSDJFFSgoStdjibu3pscnWelQ36ogEHInJo7aSoUbtGiYy/2TKloVS9ox286BT2yKxfNbfPHGANqyq3/rt/DWMr45XTbtp2lLzI3jEKJ2Zyv2TCVtHb7U7hl5bxbW6qiNSR2OkemuKI2SpUelGu3Ewo519eE2zsOXP4/4ZR7I5ktpbjKK/y0Ljsb/dGNU4tAD+Q4Or5vFWiM76YYYasRylE7YoY2OQVNp/ascoXWN/ncj5awfFbHbZ7dr5zD/R8o+iWi6nrZv+iQ5bi5QinSxHkNqdeHz4dwmr4Fs9LFLjsOqFhTUTSnSiQeisH9IjK6qlOEJJCsdHOmbpkcCoLoOkcjUpR7K3Vt+A4yXJktSuUWLln6+1bJpwSC5j83qU2OV/sjsq4uQmte0BKlqK7FX9sG8vRjUOry7+S/k6/ngTzX5EWnRWhiSJmJZM2BI+GyUpFKSOHYOYFmyh1XvWRNYverSoSu2SBaQ48c99HnsGSLrln5obwQpSs7omVysMtajQpGrfVowqHlG9UPyX0jq+5gk//NGPnBblWGemS0g+YUuMj3RM+ewo66+r9z8m3VEdUT3C2IYApWYic7nsXC1/vatWHMaeDGwY1+qfWHh8pEUGo29pqmYyfPVfSo7I5GY/NsuoEepRRmA7U3EUdWpPx8iWUqPIZUetkFPDqJbuidSpJ212u4ZZBqm0+x7lxKil3jLSTB4WkRhkg6HsguMjLSr8lmb8Fav4/1OEJSo48ce4fmIu8/wT82qwssz8ONNrn5mqZ5NBOjMwSNAWOV2pUcdYi7RBqpPc029Hj1S6lrEVMhnachW0M0YiRT27eQsoascNf9jAVrvAp2rub/hbOUhbsWqouvBfW3DSj3784x85jnj99KfmQU8/PfMnbIo0Rz+M/dGZyRHtwB2pfbRKjOj0tbh6lFWFbMQZWxzXosGR3HdiS3HU3E/XprfUsh61r1WoxQhfvnV7sCtWKwz+Q0GJOhlLZNeP3RIoMUtunf7TM396JoYzztd+GNqjhNHGwZHMZM2lanLsSIe1YDt2Q11AascttnYhSGUQpKzGiMj8myWOiaRuf4MTatbUvgj+mqt9uzCqVC3ORk2OesJZP3IrCyMH0hn2ccYZ8HTG6af7YdpEqzbR8c+hRqfKHr9s0dZNqFFchWzXzhcjS+MoO2GLG2wtaba2uToFKW6MWDlqd2LN4wtDLfqWjYpUrlYYZ6PHF5x41tk/Mhyd8yP4+HESpJ9IkIAjCxE8dex4Bs4VpQxSKf01mazVdT1+fWxWZjXb2ux2miTe4B9GttyJf9NceiQ2+YtqduKGx+COrMeWvoj+nqt9a+qPNQ6pVqiKjahEBqJzzjKLtMiyJED6iQbppxKknwJIwJFddex1eh1k5czTfWxLEHR6VrKmm2pRpz/w2XUDjtq1S5+AlIFR47Q9StWP5A62FtmDbC1Obla70/GRFhUWflv2PNY4FAvWSotqHl/rxLPPPovWORzVzvEcRYqkBYmez/hpRxPeDBwGJFjmTcfTPVBRXMvI1erKS5QgM7siOu2XQ5GRzW5UygySZik5geTTNXFbyObaHJ1c54TjawZaVPPbglGFQ6uL/z2cFtU66+yzf3bW2eYCRToHxcg8pwLbTyOMwCAZhOCq89M6Z5xe50zQJbYxdc6s0/H0KKh1PD3srSXUKOj1Z7jseGjEkZTK+xvlpCg4R1u3aoMGmywgKYd0cpsTYi2yGH3zJ45IiUIt6nTWz8w662dnnQWC9CN7cVg7x2pSKrQJkliMzjCJW0fzUeeMjmcYcGw+VgcvPlqGpemM00+Pu/xhJbv9qSmLnRnX6of2qB2l/ypfa1y6QcoxEtlMZ2xsj1J3PTq59QmRFpnrm69GNQ6tXlgYatHxhZ3PAoYAo7NYkVCNzlGJf2SRfvrTyGobjgwjcDiWpQeeOtpX84HNMBvpOibimuqrcVOt7qmxQ5Igqa0ibUKOMmb9E0fX5IuRBMlVkCiihSS1O6Hw+FCLvvnnQTglElpUeHzNLj//mV/OHZ1FgoQsCT1ikn4ahTYWJEOS0SEb2lCLTGg7vba56tgnEiVAqWPH07NSNbE55NQcNlt311Ic0eftYo5y5f1NS2vU6gqSmon0JruVzfi1Fn3zLbaYSavplajrz89lhM4CRQIxsv7Iq5ExSKKmnZ36cxnJqNEZHd2eNENSR9QlK0dAEv7sCaXM4bW6eGRWzFG2P6rbpnW7dgFF7bwo5RyIzF2KDDN/cTu/rI6/yfgLjw+0yGL0Td4bcnA1WbCmQRCA6Oc/83IE0e0sL0nSaEuD9OMcKRuUkAxHBidpjji61elY+3SvIhKlcMNa++DotVQVsnZw0kjdNg2SYc1apHYhR2XxRy1bZhjt5s2ayR1sMltrioWjIFP7BpcfVcWas4eCX/z83J//XGEEekQcnSN8tq0fqYztxxklpJ9SCQkwOoPrQhDXDFP2qbY1SkhSQpSiDWv2lkbtc5ijYMuR4SjM+0mR2rWLA1uixaYxapKrpC1ntNXsCGPU8qSCRKZW7Zt6cl9F1fagZSCC9bMApJ8JQTqHa5GMUmm1yDP4siAZjH7K9UUT2Di6daxjSapzuvQ2deqcmiCpfVA3yqxDKo7qt27YsHXYGgGOdMbWqmxhLfMwLWWQ5EDkyc1qFyT+4r+hPbWKVeP/mJoMUQgS5mtnn+Uz/2RYy8z8iaUzUJFswoYpG1W3T69DLxTghL0xJJ0a6lH7uvYWtO1PTWGUMXyU5ogLSEqQ0hwFJaSy1CGbBXJkMKoZ/+/7jezwV6gSQ3S8geg8c0mQ4siGGLmEDVpsP/px/iSB1/aHP9TxfRJ6Or1OR8GSDW51wkPX24M5ah/FNVmHrB3114zPjkFqRLLkY1urVB0y1KPsgnYzPYEEHLVQipTGqMo3r/pY4YgkROed9/PzFEiCI9Cjs4XNPke2/KVB+nFGwnaGFyQb2M4IzxFhTYKp2I5OlrAMUFfXH+uCHiXdUTSeLfoiDaz6tNYotaPQZqtI7XLma5nnIMXZmj8tIjET2ax2rQRGR3zTMKpwRK0YoloGIrssROcFke1nOrKdDXJ01o/OdiWkH5+TNYYUZ2yn/5StdpzUu5RMhjYWFqVGAJGQo/YhS+JbBUf1E4VI5MjS005Ht8al3Do7j92QzeV+WgLp5Oa1O8UY1fqGYcS7PSIlOs9x5AXpZ0HKxgVt7oyc42x21vRIMvOnGtIZHQOU6nD/rK6BRrscBRJQVJefAl3KPiLCctS6YdJpt2snZ9lamSu1pVbvhmxaukFqzhlbCzza39a0T26RwuibtTME9p3Fxvo8uXIaJOePSJJSke3HQbNWNdnO4MgGFOmex6mIEoDUsW5dqUoqthk1QkU6NdSiqE1bW/uj9sBRHNYsSY2ApXY+8w9CWqv8w1owod0sUKM4qNX8Ju1Tq3FoHM5Min/++efZixiKMfJ6xBVt1xdxKduP9VhkVlyjMiQpUiRHp/rcvk5Hi5FhQtlt55EsR2iQgvAWkRTMQ6b0CLfWNoo3RJaS+OdAifeMNFdJm7XZ6YS/1jemF1LjkGSKfz5QxBgxS7kSNlXQPkvIkUrZfqIlSdYhndPu2NHNtLljQ/U2WRPeDEinemmpi4Vs4Kg9SFJ7fsMo1YlrkaoxYjBqHVEERrsRWiTM1xonSco+2D8oZ3t/1FzXj1omM7Vvyh7+iklPdD6s84QeUVjLEiRQJNWpPUtw5JtsWXNIZ0iMyB+dlgSpI3J0qoVIOqW6QFH7U+ECiOq2F24p2RhRCVtrTNii0NawHRa1ZVwrS+KfufXIH4KUiVH1b0bZqELVbIgSIJ1XSgXJ65HkKMDIKdJPM+UIQeIbhfqg1tHPhfCkmgfJChB4I4psTo5kbKuT2anFAe0wrjUEiKAY2a4MkS1Dj5p5kpzN5lF/3wwRq+o3IVFLFIpqFv7igvPPv4AYCiCKMQoqSGedlRYk5ZCyEzbPkOCIrlM7pm+mBmxIjtBpOzniyJbAKBAkm7BRJbK1poiddi6L1CQ78Y98dnTmiHmc3PSk+HCNwm9Avl/j8FopiC684ALCKEOPzjsvo4QkSPqRNkg/1g4pa8D2DIES3DbkDESI7zor7u9YV4FEaNRlkBAj67HxEoJUR9wDsrYWpDrtsePfOuGRMF9Tmb+FJ6uEFIzWZgyOuEOQzMfJrU6MMar1tc/3/WyjWN0sRBKj886PFCksaCtF0g4py2lnVSLPkAXt06gUeRpwZIXptPhOWKEigbN2PtuHNcBIRLaMFlvdNg1aW4pCjKgtkjjDJmentkm2PWqpjLbN1AxGsTX6midqNSonIOoOEJ1PD61GQcYWqtHPVSEyQ5GSNaRgvPaMM6Q/Oo1vp25XR3g5VYJUV25NY6NN5SNCKBHW4hFtxREsEdfatXMcqU5taU67lC1souufjVHlrzVGBycg6nSBX+cDSTqweZJ0i03KkbZIP4oN0o8ySpGxHJ0GDxYiDG0mqJ12mhckuJm6LAwhR6EWiXd1tdlOWKQ69RoAREqSPEdBqzZsjehSZNNcIDXzO2qRo3YnJDD6Og+t1YgnimrWukCv8/E6PzPz/7lcuhQZWW0V2FJeW2/QFv6IJOg0ZsjaI//U/rT2OCAiQapbN+CovbdJ7X3ur522PEirQcN2rcPQ5imSHbZoSLtJaLWbNMllkPxZWua5wQmxw6729RWjGodH6dnxtX5xwQUxR8Jpk0dKVyKDFpvE6Eeao2guMpH6u7z/NDTap3FQoxeLUHvUo3rtSZPaS89Tt2779GK/nWyOqM5IO5QjT1K7diK06f1HodFukrnLv2VGZMOkv16M0df4niHx+bEGoosuuEgj5BVJGW0V1zIzNmm0f8Rj2tIixXoUdUY8R6eddgaRdOppzBHKUft66KexfO30KBMjims6/Y9L2nXqtmnduh1jBDaJ+2veI7XzGOU4t1bdaSQrXUM1alEnbql9bef5K0fxrGYBKFEAErtsb7QTrRExPfKzHIEt8EeOo58EnZEII8vQGaeJhRzVg496p53aoX17G9ewet1eCk37HMvlbNHe7NhoM0hSjRijVt4h5TtaG501KjojLRJF7Gpfz/upVbI16wLxsIWii0KCMiNbzowt7I2U4pBSHbaUPepo49oZaLB51cOX9kBTvdN0Ky3gqB5IVr1Yk8LUv7ZUpDqSo9ZBVEOQKLiVcl9IjmoZkuTVqEXTk8BrqJ9O1a/jPH+NI8LQW1j4i19e/MtLLrooIUeUrYUuO3RIQcaW5uhHUZOtFI5COVKahFrkno1B4uhmnwOO6rVPceRJUjcVrZ3FEXskAZKz2q0iPYpGR0Rgy+qwtWiXyPeP+BpaIyg3ain6xS979Ox56cWWpAswsiXk6IJsPQr90c/VuP9ZZwVylOQoyyDJvF8i1EEoUj286sFz+3qkSRKjenRFHFFka8/n+4VOGxN/qUitdQkJQGrnp/2DlC07YYsOiwCQWrQ+IRKjr+H9i+wGxkKdonX61aU9zbrUrIt/+cvLUgwFIKEaRal/ENq8RYpASjb9hRr9JFHM5itAqV6H0+Dq0IFim5Gl00iSkCODDwgRYRSy5LsjySkkSNhiRRJq5I12apt/k8b55WtcP2oTz3x97bY6um1DDvWatX5FDOGLlaTLUh4p1COEKGdkY0X6eZT5J8ci9SF/2h6dRoJ0mvdGHeipgyUJ3nfoUK9eB1ClehTbECOOZvXUFZMkumwCpDrt2yiOWvsRJO+P2rmMLU7Y8pxkA5SaN6trErUCJUdft81FNaoUBlJUs+DXlp6evS6FBe8NSBzcQpKiHtt5ORTpZ7qoreTorGRrJFU88hxFYmRFyAgQKJF9IpAoxNnYRq1Z5KoeyhC8T6ZtmU02lbD5/ojTI1lCSqVsEUdNMhWpWbPEmEiVr5M1stONBUqKDESXGoJ69bzUPF3a05GUlCQf2bQgqfmRKLAlLdKPQpBCo/3Tn6R8dowRK5K56gFN9ewb0iSLEhkjXO39Mwe6RD1S9UbcaG1okPysPxa1ZUW7cSkZW9McJe1mNG2k9OjrNPlI7TOpRb+4uLcRIqtFNpyRIiFKl6Zi2/kXpIz2+UKMojabHGfLmkH6caRHXpC8PTqNKpBajkCRUIzqgSp1sEAZhDpgDQDNtmMI6MFHFNoijupkc2SXrkO2yzXtH45EZmPUvOGJgRgVfJ0aalwpElL0i1/9plcvECP7ZOEhQUKSUnb7/At0/SieQjov6rLpHUjZTjszrv1UNWmDnJ/9NUgRfQBJHepB4oaeSKqRfwkqAHV9tSlu1datrxmyRrtdUEMyV6scU5F5jdaeDK0Qlad9japGh3tnzetXPXr0QoJ69cR1aU8vSLBMbIvd9vkXlBLYfp7ZG/mZ3J4d6dGPsvQoECSVp7k0jVc9fnTo0AbTNszRokUhLvDaVpL0PbP9lpHWcWALa9rsjxoH0yNNGmcfX6NiWvPaUUmv4OuS7uNMkczRav768t5WikCN7EUvcGmQpNs+P6xGnpcxYZvdr/2ZHIs8K07XFEjR2Ehgs31II0GKcbKKlIBI4qSCW9xlk3okeyMNW6uEjepHXpBcBbIUPZIFpOatTlLOyH58TdL9iiKewb9ZzZrGFPXqZRkCNcKYRgj1vDRSpEtijySaI8lxttyK9DMmyCuSS9h+IgpICJKTI1eBPCMIalmrXgcdznJAVC+MbHXCUqTN+yNB0l22RmGzVha0zVW6QWoWWaOvyd1CatCOfBHOfvHLXkCRXZcGatQz5KhnCqQgYcsYsJWKdO7P/DF/TpLSkyOJYcgzdF8kdEa5UGoDJYDcK/TaaaddB+NagyCySYrYZreKrbYJa62aCJCapurZzZo1OCHI0gprfR06Idz5cEpZs9OvepMUcVDz4QxcdmCQeqZAChO20reOWKttQEKWzhbBLXXuSGaTNhHUMhBqQx+Q/+vVJmmUIo6E1XY7RhrEgpTyR+2SJrtJk6yRSAapWbM60f79r0EnxB2Y5m6hV/BrJ0QY1XpxVIMLIQpAwrTtsouynfZ5cS0yGo0812LEinS2vc4OJInztZ+I8tFPEzbbc5RSo7YdOtQXKNnIJklqE5PUPqFI7cMt2tRfa9A6aPnbjC0Y026XX0k7NTjSHKpGKlH7yo9Zq1BVZI34+ovLe+l1KeX8EqbYINn1y0suy5mxnY8ZWzhhe4Vy2gjS2QySuViRfpyIa4nqUVA86nBagFBbfGrrUEKaEJ02RBA+AlXyPZP2Yebv9ciWs3MrklSjUvP+KGFrHFgj2wn5aneoVTisoFBrUc1f/Kp3r2iJdO1SX0TqFXOUjmy5i9qWpCuIpXPBIjFH8DgbFge3xH6Rn0bNNUuSDmtCjNqKB3wQRJajNgBPmzZtkhB5lxSlbEqP2rROcNSutU7Y5JaRRoHTztlia173BJ2omefDvlKMKlUv4FVIzzae9enV63KFEMiQVqOelypBujg7aQut9nnnB3qkBAnCmnNIZwNJiBLs1fYbRn7ykyjrP8Nla6edEYU1GdD4QZrUFklqQ9B4ipCrpEeql3TaPu0PI1tstRu1E9NsrZQgtWosjbacPzJPzU4y3BS4h13Vv8raY4UqBQW+Q4yVoiutFF2elCOlRwBUkLFd7OuRycnIHF6bBIljG0J0NjttixAv0qRgN21osxP9WSSpbQdBUIe2AFD9tm0xull8SIvaEEttPEptpDZFbX9pkOzGbMNRKSDJoyJb6ZAGcpThtJu3O7Em/Nz4Mih9hWc+1jisFtJciP9StvPxy6tAioCjyzVFeEk5gvpRIq5hqy1Hky2qIlHmfwXJ0c9Zjc5lbwRSBNfZ55x9Dt1Li6LaT3VYS6X8YaIGLAFFuJCj+sQRMeRim5CmVGzTlUhs1datB/aoQS5/pIYiUzeGzMKozQk1BUT2h1frqzseq3I1hbSButav+vTp06tPn8tTenSpMNk9maWeaaONPduLcnnt85Iz/1d4kkxs+9m55/7M22wWI3g2LJ1zzo9kY8Sr0U9lby0rrLUFhNxLW7ZKbcJVz73SlbJIgdWmLi3KUTheG2Rs7Rq1StnsJsForUz761D08NdXNsxf44gQogITz/pYjoihyxOhDRP/njK06cDmHFLPi+OcLRCkZLv2vCtcWEM9chiRGJ11jn2ccxaAhCgFHGWokeOorbzaIkhGj+pD3uawCVFCkaoXclRPZ/5cQ4KsH1ZgtoP92X4GqVVUisw43q9Z45MKAooKvqra4yHVQ4p+cWUfWJadPkxQIvG/1HVHMGO7NFXU5pwtnkVKjbTZdQU+/dxa7SusGhk9OvdcabTP9u6IQHILJSlxPoRjqcNpsqHWVsiRX4YkA1KbXCu7sB1l/nXbNPAg5Yhs+mA2HdRapTBq2eiEAKKCr2iDWoVqBZqimp1+dXkfWqxFiqFL2WNLgw32yKb9PTVFLEnGaseBLdSjcMDW5WsY1CxHlKuRQbIRDdToRyhH5uO35rIkqW20ockWQQ0hCjiy7+p3yI0R++5UZAvimm2KpDBKHjsiDFIj36kN5QhBatK+k3bYBQXVvhKDfVggRTULfm1UiCi63AqSI0ipEnTVeglnRAWkhNG+OJn78/ZaxVJfCZIVpSu4gGQiG4Bkr7PPFnp0lvVGrEc/Ro5gkSaJyaOMjkjbtpIlttkQ2HIBJJ23KG4nUn8sQiJGIUtBqzY+lq2VMNnx6cdRTKt+2FcA0cGBFJn87GKDzuWXO5JYjBJpP1aP6EKMYAoy9toXc2C7LDJIOSbaLEHn/RxBOtde55IkidoRWWzjjSiuWZAcSb8DkuhG2VKRlBoFCAmW2tQvXY7aqGqS77TJDpuQo7QkqWl/MexfyiBby4Y6T7Ni9OXPiFSoGsezqy6HxVHt8ssh50+aI65B9hQNtp7kudMrUUOKY9t5faUgOZAsR6hG53JY42ztLOmNDEI/Nhdi9NvfmUWK1LFjx9Bn+7jG/kiuDm3rl45RmzYd0HTLZluYsVl3VL9BgwyQJEXtmKJWjdwdIliPsJ4kB7Sb1zmhZiBHX3ojpMYhBaG1/vXlfS6/3HN0uTdIKTHiRi3m/QAQDGljlp/BUTTSllAkAdIVWNK+ArJ+kCNbQLIlJOboHLBHzmWzPZIcGZKuxpZIAqK2WDdS1qiBEyQA5RR4rm+uUwid+oEiCZDCojYENiNHpwiIdLqWUYds1Q7kqFWrHHNsJ4XZ/pc+y1+pagBRzV+wFEmU+lzOFtsGtj6uiO1DWk9ySuizwSz1DDG6OM3RBXRGWzyH1NeZo/Mg7Wc94rgmkzWKaqRFLEi//YnA6He/u/rqq0XeH5ijDh1SQc2ma0RMfQanvn/raerAdYCwrC30yCdrgRo1TGz012fXZIa1ZvWDEnbBlz2EXePw4J9fUPCrazRDgqNeffokpQgms3sKbwTzR716ppJ+l7Jhxnah5+jCSI/6xoXIK34e6dHPVAWS49qPf+u9kcPoaouRWZqjUryRBan+KQxLfVKi+vXb1Hc0eaZMaOug3XbgkNrX92KUSP1lYCvl+GOHUcs64Q+x8Ms91ejgqGr962v7XHnllVKJvEGCejYpUh9pjPCFSUKbRKP+2Q6JDdKFSpR0Vbsv6tEVENVcBcn5IxfYEnqkgtpvvRohSL+/+uoOrngkOQr0qAFcGNUAoPoA0Clt0C7Vt4v5cqok0zYd2OwubSVHYSUy2DXCp47EdcjGfm92y7YnhiHtSzXYFY8IXFFBp19driByitSL/VHKYMMLiVFPtto9/Z7aTI4uicPahcFspDBIV5x3xRXWH10BVUjO+1XWrzEyDJ1DLvsnWo0sR7//fdsOXD6SOX8sSA2sx67v1ymn4AcKEqFkn1CV7FBAhzRIlqN69aU7atAgmPZ3WyHDLSPmI+yxkTWq1ylUoyO+xIG1Q6prhmzr4xqg6MrLk3Ht8sgdCX/dkzN+Cms9efNRqsfmMn88zyaMbBcqh9TXXFeQQ7pC6JFZZ8PTz3RjTYNEQU2aI6TIcGRIatvBz6+17SAhati2tbnIZtd3MeyU+qcQTIxRGwcSRjrLUQfVa1N61N7IkUzXWjfQm0ZaR5VIfxet1EBk08a1v8IKdsUqBWoVFvyin5UiXIHFvpzkyGlR5JF69HSKxPFMhraeWXoEBukiH90uvEDqUV+y2n0567/CihHo0c+dPfJh7ZyzJUQ2poXmyOvR72m1BZKC9qxcrZkjIUdIzSnqi0wUue0OMrZJPapr5Qi/N2m2wxOQc81DAkjQCFE/ySpfmhhh1dp/1Oz8qyvFCuWIe2sQ4DRDPYQmiSpkL0bpUnRI6aa/rEReSKqkUv++IvW/gmqRBqIrNEfnnq0TtoQ7ijBCkK4jSZKVIwFQQ7ZHToByrzbMkydJCxJFtfrwUb+BZ0lPIGUUtJN7s9ufUOhmDOHjS6tgQwNN/INNPLtGQaRAki7bOew+CZR6kCHi2hHr0aWZeoRG2x3TdiHp0YUyrvX1GF3hDJItIZFDEhAFIU1Y7Ew1uu66635/XSMiKbZGrV2upkFKEdXBc9SBCwCuuO05ak9KRN/VICVK0TFIosHWSrrsxk1ansgTj6xJX1Y77fDqBUoHa/5CSRGQFKRrfVCSLg8ddg/3ai/vj7w16om1yJ4ZJGHifxE5JK9IF4YJW9/zHEoY19hpY75WmhpFHDFI11mSrvt9o7beZIMzQndknkCOGtQv0+pgLtCjDq4g6TiylWzjsBqQGFF0a1sfPxxO8vwaZ7TjClKTNifUlAGtsKD64V8KRDxrXehGd5UUibimJKkPcdSnT6YYWZBChHp6e9QzW4/AH13oYpsx2hdeIPwR5P1ktC1DWEEihgCic4UcnZ2QIzJHQow0RmY1RElScoRxzWZrZcOoPmduSJIHyfqjehahUxrYqz6DRCw1sOJE4Np4alx+QzZIhqFW8WGjLU/y06pf4gx2jcNrFSgtqvmLaw1F/SIxCvN+b5CwCtlHYtQDUOrRyyX9vVIg9eyZm6OLXFgDObqQOep7PspRX9/up3yNV0Zg+20c1gJzdJ3g6LpGDV1HhPEhPUpj1MBD4H/dFr1djOtQn2ObTNhOsRYbKCKH1IBEqT7oHn+OEwbYlIGCEoz5a4qatj2xplMi+LF+KbcsOpjjGSlRYa1fXWMYshT1C2JanK4RRuiR4mSNKCI1ikG6NDOuXeYw4nwNBMk5pL5YQgJBIn9k3RHZo7PxSvkjx9HvnB4phw0QOYyuI5MkQGoIJLUOMIIfMz/hawNFUn2X2nUIBamuSfnt7z3FCxL/YfTHYWxzKPIL/Cs1tLf3Y3Ntxah2LWmLbEw78KXHCkeIfUMgRb++9pp+/foBQ/2iqHallCKQIzE30kurEWiRCWo9evTgtkiIU0Zk4421F5FDuhApgn7thRjUrCD19QUkEKMrrvg5u2yFUShHYR07BMlzdP31qEhtNUfgsoU5Inzc2/r1o+cGBBMS0CaMa/VOOaV+Ax/V+Hvwj23QgB40eokuqw3PzhFN7RCmlg1ZjFxUO/C34atcrUBrkZGifryEHl0Ol4xrfcToETdFElbb8NQDOGJ4esT2KD1bexkb7YsQInNdeIFI2Kwa9e0r9Mhy5DCKOMpO+a8OCpA2V2M5ut4uq0gMkuRIaJEPZrqS2KCB/0p9AR5hQCBZjGwZvMEpRJIx3I7ABvTnN0AOLYS4O8U+6rmDTtpYyTTC1LR9La0LBQd8kp8Ljv6f+etrrzT42AfFtZz+qE+f3Bz1QEHq0QO1qEfP9MrQo4su4Mv5I8sTgtQX45rL1n5OJvuKpB7ZKmQsR74CqTN+FdQApCaNnMVuaB8AUgMvNA0IFSJIg0RfgsTLkdTBT22DQcJuyikY2OonfRcrUQceHnBbL0/jcma9evXrnSS1qPBLKD3i5iHxD+30qyv7eS3qlwxrl3uLxOO0ybAmtQijWkKPqLidoUc2rF1EGDmjfeEFwh9BWOsrzFGSo3NyFiCvzhHUrrveXgKkhvxk7VH9LGbSq61LuQikDsJot3FaJEMbh7e2QsZAjCgqghq18cOVdeu2r11L9yIOtBhVqOKTfHhT69fXSob6RWIUFbR1VNNjI70xW+MFDPXokRYjbrSlOCKSLnKCdD7VIiHtB4r6uvaaiWoKo7NvAI5uhI+MytHVsq0WyxFhdP11jRv6oNYQq9l7t0CY6tevL+TEYtSA1ag+lpDq15c0uVwP+OngauLBhG69uif6fakoEgd2q6x3RZSgdfcQ9RNRrZ+OarII2adPH0FSr4Qg9bbJmpcjwMg+9QhiWtIgXXzxJahHLEgXsiCJtJ8K2r4GaUES/ugG543OzqocSXPkxOj3xJB12cjR9U0QpLacraWEqHUpX4DyD+dwfjuS+fmfYhcVjyi01SecRL2gDbtrQKiD237i5irrnPBlilGFKqx9hSxF/eJ1ZUqQLlccSZD6aIJ6c9aPHMGjRxTTXFyLQxv2aQEjJIkyNiNGziH1RUEyDCFGMqr5GqQl6cZErvbbZFdNiNH11zFEVpCaNGoo7FHbEBiJTOvWiqTWrVvTExgk+2CnTTHpFLfq48hAg7i7IjK0etJe+9mTeifWkglTQUGtAylGlauLfNC++cW1CYTIG/XLimlBVOsTNvp9XOvVwxHUIwujRKPWcWRC2kVUOzIwmWUT/75sj+w6j+XoiiuEHpE9upE4QpJuEpWjQI58d1Zy5Elq2qih8NqB5jiOeNCjgfjgEaK2jFd9sEgc1yxGDUiPHEipMngHdtcoRB2YIR4ZOKFQd7WqHzgxqnGEs2GIbqdfX9MvtRghHdjCjojK1vp4hrTNpjwtaY8uddEtkfaDO8LIhjk/LuSo7/los69AOUKQFEYY1m7ULjtDjeICpKbIWe2GaLNjJYqw8Z804LcNUZEMSR2EILUhjpxDkiS1cZNLTo7Eiab+lIBYjA7chuvK1XX0BCm6NhXSMuKa56hPRFEfHdZ6C5fdIwdIWZ0R4AjXhRcxQYCTa69RWLuC7ZFyR+f61tqNJEc3WTlKmqOUxb5emKPr+19/8/XNGjt7JOKXnnuFT+Oj+PkX2zqTBH4HOWpjCUJBOgUHKaUg4dgSuynwRCKiuVXnhEL9sz1wYnS4psi4omsMRPbqd22EECpSv7Q1uvzyPkFYi0x2714KI4dTjwyQYo4uuYhctn1ikFiP0Byd39e77Cu4K+KD2g0eJFqGpGR7NsGRMkeIUpOG1FiThxM1EJcUoxAhniAS9Uia1kYpaoAYUXyDy81SthHOKHHOBIhRQNGBau1XSklRCFFQPEq1aeW8iIRIpfy9hcWWLCWqR9xji0D6JXHkCpEgR6BHF/p8za4rWI2cHN3gwtqNkPGDHIE1uimQIxXUrtOVo+skQ/bRlIqRrRs0aN26QeKAolJWA2uR2voaEsJxSmCyT6G4BtJ0it+11EFkaPoWSvVqywmRA9jahzOv1DIJ2rXXIkNpllKdfj15VLoa9e4dohRTdGlG/ehix5EJaxdRYEOIzNVXdtf6OjkK7FFojn57zk03hftDovasrBxdp+TIBDZw2kJicgQxvOF1O9yUr3GCvI0FyakRo8SGG3dVuj0m9eQpE4Kjuu25gO3WAWrtV6oWStGvAKKkFF3pPuLiUTC+lm6J9PaSZDDqrSNblj/K6PaTP7rgwoucGEG+ZhECNQKKXPXIY3SDDWo3AEc33shydFOgRlenKkfpoNb/+v439+/fDJy2stIROPKzdoARTMO2JpRsTGzr+vZtAjVCSYLXNgxSEiNxsxsjRoEzqnYgxKjGYQGsBb+yCMFHv2tjfyTy/iyX3SextBbBk1GjpB4lJCniqKezR+SzL+K4BiCxHPWF3Wt+ySIkL+uNyB85kDIqR0GqplDqb0C63paQGjTISVA7RxC/o2HY1l6UwG3byKYwEv7oFCTJnwsgjJFK0swKxajgQBzSF0pRAUrRtY6jfv1CiK7ktL9f2holQZIMGS2yDx3UegqWUpl/xJG9jbZbrEVQhHRpP4Lk5Ohcx9HZN5x7A5ojkCNSI5Oq3ZSWo4THlslaf+TIgNSyUcNAidppevwX4L1d5gXuMIOyhBy1psgWRDVq1LYBhjxFXonaB4dvWzEqOPBidEhCiq51GHlBSrmjflkVyD7BFtog4eeX3oE/8nGtR15xzWf9TNIFmKohRn1dj7ZvLEeUrN2o5eim0BxdHZSOsipH/R1JzRo1DHUI6BEChOzQc2u/swPPwCKUSI461E/KEYiROl0ieaykBSkSo/2/N80e76BW92v7SYzgkVE6osGRjEL25TmiGuVqvSOLTX2RjJWZ9VPGduGFF0mO0CABRIIjn61xAdKmaqBHIEdpjH7Pg7SqcnRd4I7QITVv0o7CWkPExyOEzLRu514jkgxLrcku0ViSxugUjmZwnRJBFN28vX0kRvv/8IdDolrRtcHqRy6p37WxO8KOSL8rM0jKwIjMdRojFKR8XbbmyHZGfDX7wgupdmTl6Iq+vnh0hXNHRBKCRN7opkyOfh/2QyRI/Qmk/vajectGxFE7falH5vKC1BrCWoBQfYeS0CN5NIk849Y8oprR/hYjGpT1FHW+NrEEQnFci1siV4pcLabIvvRGa9QrdkfCISW1KNUUuUBydBG77PMvxLFa9EYGor5Cja5wDJ17A5sjytVs/fEmb46ujgrZ12VHNSwf2YTt+qaN0R95hFq3C/Fp1Ro/2rWKSWrNLsly1CZLi0SKJiOaXnU6BWK0v0dnD65WmhRRYIvF6Er3Gudpmbkazx0BQr3JYwcm29ez04l/lK0Zji4KOLpANNf6cmCzehTXjm5wgc25bJ/0p81RENWukxxZKYLEv3+zxiKWIT2tERfz0qo1XIKgVgmQ0CC1bZuMatph6yMA68n7/7UPxWh/HyESNj9+8atrk4vrj9plX+mKR2mS4nYIbxHp7dP9tBr1dBXtPCKbzNYg67/IBzW02X25lh1gdAPXjkCPwB4RR/n21cLGGuuRBaklcCQjmAHKiE8reAJuWrUCfuwnjZCkRrxpkdw2Jv71EwA5hsJT29VttsAZ6ZrRfm6DVAwrjr++NkuLpBRdG7qjaL9RthzZrY8gQeSReqc5yqVHkc/uGWRrF7h0zQsS1SCdHJ17xRWqBHmjqEHelJmr5ZqklRYbHRLpERvp1hjFCBr71IqfYIcin0vcju4m6/I2q0f1M6TolDb1TiEt8sm+HA6BblqY7O/XAexDIynqd2326pf02K6WHXKkNhol6ti9e+fCqKeLbXn57IsvUTn/RaJH2/f8CzmoWTnqq+ToBsTIBLUbSI44qik5ujrndjULUnPtjyCuWUGyHInMHoKaJYkIatUK37bCe8eYt3gWKN90rx0b7QyMUI5yiJEFqd5JoRjt19s4BkfN1Pz1b35z1TX9SmMo2Vfrl2GOkola7959UI5692aQevRI6lHPHOPZuYtHF4lkDWMaZ2uymI1ydLbP+m+80akRgfS7dJc/aKupqNaf7BEsk/hzjaidc0KtHEL4igtAsk/utPR2TJKJaxFDoEX1AKV6snit60U8rSZXlf0IUeitO/3qlt/8JgdIbIuuTXXYrszI+PUwdrDPqHdvr0a9UwxlhLXU3Woil32h5gjVSHJ0rsvWMKyxGlHtKDDZuTjyGDVnMfIY9W/elJQIGWJHpABqRVA14q/TjWRdXGvbOmGOLEU8GCIORm7vq9fUkz2A/toeWFRQix8moF1pKTIfVyVB6peZr8mREdHn91uwL4/kqJey14Ki3lFgy5yHLKV4JCNbX+ezdVjzDVphjoQcpfpqv//973+fEqS4Aqk4ai0xYlZikFpZkBoBTa3opGvWowYNAoZO8XKkAlp4U2Tqyfof9X48zqhCNUlorYJClCLD0S23ZCiSb61FACUGRjCo9bk8MSvSu08vX3lUgpRvHTJ1bs3FOqxdqAxSX+yJwGS2wOhWdEeWIpajs53H1n21MOUPPHY4LdLf6RF0/NsRQYaKHAgJmBo3bkXngLqwptWojQUIGDIWO2zHKnNU70Qd0mrtt+OMahxSK5Cii4kiJikFUj/d7I8rkEFIu9IfT6NHaV13X1IUzR2Rxe6ZYZAio33xJaE9EtOQLEUiqrkGrcv5pRoJOUoPHV0XhDUHUn9pkJgjzL7Mw8etTJAaSbcEJNlsLTJHFiMQIwvRKem2ft32ddvU7ix+1Paxv05S9/uHEM/CX/f5jVpZIF2bkfOzGvVTJjt3k99VHlmJEkY7s5ZNO9c0SKrVr/TIRrW+qah2A3MEUe1Gl6xhqqY89tW5KtlhX81DhCChHsGhnq1KXXB7BgptXAAwHEUYWRE6BaRIGqNQjOqEYrS/9hThpKzXooJf3fKbeKVA6ucUKRXc0iNHmqPefURnX3HEBql3yFCyCplI1nrqpN+CJJcFqW8U1s6luGbDGssRD/cHcnT11TmStQilgKRmreiOHpnwNIGPJkgRPjUiPUpjVO8UVKN6XovIYUuMYOZRitF+mpytcWgB0UPrF/2Mq/7NVb+xjwCk31yjSUJ+ri1NixxHV4Yc9erDkYyCWS/ps2FgJKlHGdWjMOm3HF3mTbYsZfdljkQp+1YqQd5AJvuGGxmjm26EIcibcrb5o03Y10l+AKGAowyCmlp6mjS1HDVpIu714QMb2KOAozZgjzxCFNF00SgcM6pVcOh+CWnuXGInRdcYiuz6zVURSOn838GkvFG/YLtauozdq09v6uz37pUQo2SzP8MdxSChy76MSVIgQQ2ybxDWzr3i1ituIIwwrLE9Okcla3kcLUJi1DxZO7LpWsvG7dqlGSIZsgjhHc/hNrGt+K56bI9ah6laPUj4FUntg6oRjRmJyLN/DhA5GNN8p0ad+l3l1m8ikGyw0/l/hsNOVI4cR2r/NTkjMkZJkvJqilyaLGdfHLkjrUchRq4jYjFijqgfclPkjsJtj3HpSHXWBEPWZjdv2SQCyF6tmppnK0JN4NMmxJSXpMYsRzFGjNApvu5oHwIkfYJIrf1ztJqct4Zc7deXX3XNVWqFioT5/5WOpH4urF2bwxtlNPl7YWe2tyCoV+iNopjWM1eLNpX0X0ZX6I/6Xij0yIW1W11DxHqjG4U7uilDjn6fHKa97rq4dORRAo5kctY0CGpNECq49aLFyt6S0TLUivTIcNQgcEZtUI90WFMWux6MGQkt2i/z1xWr0h9Yi+vWfQxF14QkBShh2iYkKV09ilP+rOassdkxQZyqpSvZsR5lydFlzh5dFudqJEhX9NVjkDdQWOOxI5uq3SiC2u/Sk/1yds2Xjpqr0pHSo6ZeheDZRjELjX3bit4gSnhzDwsSLMSoddSXhZCmyo4ao7on6epgQdX9ENIqV9Na9Isrr4ElQfoNfvwmjm5XaZCujc1RuJk/vY+fWiBBNOuRjmrcoGVFSsCU6vSDFqEg6S4txzSKa7daPbr1ChfUbrwhzVHWfrVw/yyHtf5y9Mgvy1ETjxIGr6atmBxSIo+RdUseo3atdUzDplposmVzv33dE7QW7Y9jaA6Xxtq4ol9bhPpc41BSopTy2xzbkpO0ueb6tRYRRLEvApgYoN4hSplRrWfsji67iBC67EKtRxf2jeXoVpv0O3MEDhtrR2yObstxQo002dclsn1ZPLJt2sYifhlezNuWKEhN+RmX48hh1KhdaI5syYgqRqe098NqPqSpYTX7dt+njExAqyX+xEJ7zr7B53JEqY8Spd/QU4qkfiKyXZs7srmzRN28GlAk+x+9vAxJimQFSZcgddKW6K39kqOaEaNQjfoKl93XapG9oHKEGb9ZNzqQVJs/uz/rp0Wi40XIZt8MBFHa76x0U5IhoKmlf27pLJLACEGKC5DUEJFFI1nFxmTfK8e+hzQMaO5PrPmL21E/rrnSSVLvwCUl8zafuAXjtFdemZca9epNE7RkjnqEeiR0qLfUIz81kjisNpSjyy4jRbrMUHQZq9GFF2qM0GRfASabMn7giOTINdZui278kD4kW9aOdMbvObLpWpMmYINamkcrUqGW9rVly5b2nXlu2aploEjQqG0daBEYbLWjSA+KiPmQWvslpB3eqZZZBfCoVauw8x0D7rzr7nsoEonYZkWpT67EDUG6sl+/hNVOz62FDbU+vbJCmi4d9fZyRPO0vHWth/ZGGiRI1sTjMsGRimoEksnVqJJ9ww2uP3ujsUac9N+kUrVcY0fXRT2Rm2VjjcpH4Ie8DLVEGbJv4AKEWtpwJ0AiOWodahFsgtQu22tRmxPxp00/+Vqd9jWkVahq/yB3HT/wD/8zaPC9990PIIEmXcNuu881UpJ+E2rSLVAAgMStn967Fs4/ZhxOY+jp4wJarzRIUox6SzXqmVU+0qXsy4QcQVC7LFE7urUvhDQIbefeyoVsa7JvJDlCk63cUaohknUqhJrtJ46ofGTkyF5WjawAGf2xr3S1RJqUR6KwJguQbaJGiDosFOrXhbU4Mbcc7est0itX0xQ9MGTQkEEPPvjgkIfuf/j2a1iSrrzm8svJbfcpJbjdwpKkp/t1KyRj+rEP8pPWIj8qIqJbz8BnBweMBnIER/mROYKoZuPahY/AI65B3oqCdKtXI2ezqT8byFHuez+EFUiQo5sFSLbbj8m9VKBWLYUMWZCQqIijdg18nsZD/bqh1l4o0omFQA//2Pc1pB1eXVFU69H/GWLXYEPS4AF3Pnx7Pw8Se+2wlBS322wt6aprlBr1S1EUcNS7NC1KeOxcyVrqdAjrji677DKvSOyyH9Pm6FblshEjq0Y3sBzlUcm+LkdUEwVI6bKNzSalsU4IBQg4YnjAIsEvAUZNfbrmMHLD/KhIp6ighgl//ZPUD32f94JUVX+cDWhDcA0ebEgaNOC+u+4WE0PXXO6iW2kFAKglXXOlRCkuP4bTRr2BpD5x9VHpEdEU7+uPTHZchDRyhGnaZZcJc2S1yDyxHF3h4hpHNa5A3uiSfuuPSI1ui+VIRDWpRuH42s0c1W7mhwlrQoc4kAFEajVVeoQche2QNm1O0UHNFY/amCxNqUfVfYKoUhjQHn9wyGCkCEAaPGjwkD/ezyBdA5J0eZD/Z7ttKiZlp/sRRzSu5g8TCaqPvZPza0KPeqSCWlw7wgKkJYlK2Y+gHiFFj3GuditB5OWI1eicG2UF0nB0W9YBfrnPzLqZtchHtua2VETUNPUShCA1lUQ1VYJkzJEQIxjlp+FHGdOwQVv3xEL1g9+n40NqHKoD2vGP/s9gA8+QIU6QBg0yHA34453otvvZO1mz3eaqpCwlhXpEvVtGKRHUwnRfb5ZNYdTbaVFvYbFlo1+abToTMk7W8OMytEekR1aOHruQQpqBiVJ+o0ku50c9AoywApnR5s+6h0hKjhCkm509shy19HEtx3JGO/bYlKnVi5tqVoxO0lpUfV/GQ2pUraX+sK4uoA1BSRrsPNJDZJKolnQllQCuCkDKRdKVmqPUDR/6+AZ/lsmOkzUnST17ZJwJGXF0CSGEGF2Gudoj5I4e8y0RztVwKPsGSvnZHd3kU/7bolOy1f5Z1aBVJ9Najm5mgE6+GT7AZrMtainCWg6MmihzJM81SsU0OzgrfvAmS9sHiipUV7ao5gNPPEgaRNaIZGkwpG33MkgY3Ci0XXNVUN3+TWIGgFK3a3SqlsrUWI1yMNRbGSOf8Itz/IOELSxB9mQ5uox7/ReSHD0CYe0xjGq3QrKGJvtWLkGKsGYwuvEm745uC7dhJ+UoHII0IN18MwU29EiWo+bNmpKLblnqkhy1a3BKkPPjvIgy2fXqtalzorLXtarvfa5fo7KWopqPDn7QB7TBSJEjiUG6h10OkhSEtqiQdIurJiFJl4feKAFRaVokqthhqkaCFPVDeiamslGLzLrwMhoXeSRwR7fa6wrGyDKEYY0ttjVHYijb6NGTSY4yc34D0fU3A0DksjG4mc+aU5YWctQspx4ZjOorhuyjnhs48qXsk7QW1aq812JU4QhFUa3uJqANIi0azCFt0BC/rCQNvY9DWz/M2i73LikraxMk3SJJShhs2iybhxb17h2HtUiPMouQVo5wXYQcXfbIZShGVo4eg7D2GMa1W5UcueXk6EaX9N8GYe3J0uQo2tJ/MyhS/5vJH93c/2SoHkVC1KxlM3jkkCMwRw2UNeI5WtlXa1NbC0itvT+EpqKyRQW1hj314OAhag0eMtjHN/bbg4fed9ftniPO/4NKkh4jYY5cdOvjbvTYJ7o3X+/eonaUgRB/xE3+zJPXo0a/cEaQ8V/GKT9YbOTo1r6PXUFyBIVs5OhGb45crnaTT9UMSLnc0XWBGF1/vSeoP1gjV4WMdQg+kKVm/EWVrIV1bBjHDs41MiHthFraGO11R7ZSJ83jHSbPRx2SFpvs0WB6QKAbPIBAIrvtA5tUJBhJUiTdEkQ32jgbz6uVbo1kvh+VjnpwKbuHEqPEvIjn6LKLHgGOrBaBHPUlewRRDcXo1ltvuNXpkeTIJ2vksp/Mlaxdr0m6GT5uvhlDG8QzhMraI6dCLZsxQrjgjY9wwh21a+BP6m/DDVqvRqBFdYPCY6e9zvUPURAV1PrDIJnkM0TqC6RIgwYNGuIqkpj/UwEgqm1LNbqFc7ZbUJIsSX0uh7Rfi1GfXBXsBEbR3FFqvj9xyzXjji5zy0Q1gOgRUqPHUI5u5Qok1Y5u8HoUYvTbm7iv9mRCjuKzICVHLqYRQqBI1h6xDmWvkCNM1er7g4xP0cdAwKpdSzujvT2dz9oi+ScNH/qgg8bnZ6EukUMaNMia7fvuokJSPxHalEcShaRb3OMW55JAk3rDDv7LpRqRPyqdIhHfentn5CNbEN8uTeb87I4eMQ9njqB45OKapQjUiEAK5OhG2ee/TUe1HLWjp5UWOUHyigTVo9wMsSgRSIiRPREC7zvLYe2UNmCx2Ry1qXvC/jFGB1erJVZB9wcooA0Z7J5U7WgwhjOvTsZrQ4/kHpH++8h2jbbaOmu7hSkSJEUttTJ47KhwpM+q6ZFZyrY5v5KjR4QcPUZB7THqiEBYszHt1htiNXJhzdWOnrz6yexhWhakpwVHPqD15+hm5ahZboocRFSqhJS/tT+Utg13+U9BMaL7hIjxELv29vSQQ9yAif0o7PqHZwYJgJQ9khi5L9m3gxCk2+9xtem0R/rNVbcgSLfIS7IkSeodHv2QyVEy6/cz2T3itlrAUU+YF/EcPXLRIy6sPcZxzYjRY33ZGdnnG0TlKAhrNC9CJcgnY3f0++ueZYyeRnP0NMc0UqP+bJFuhjpk6Ry1dHLkStmNWsPpxmpYBG02n01zUqcC+fPf25B2GH07/jk1hz/nbA9SFK3Bg2U1aYgoJD3D6X+/fteIDoluthFJ3iChzUaG2Cj1phJ2XtaoR+CMQkXKug1EdG6WXBcaOaKo9kjfR/oSRpCskcm+wbrsRFBz9ug25AjMEXPkp7KNGj2LDMlzIW6WUa2/cNqQsZWmRRTYiCPy2IARkdSmDZ0p4gxS3RMKPEPmY+/OoIFqkVO1goJhDw72wQuz/MHSZsf4yJLkoKEPqawt0Wu7ivdt3+JqR4SSD26sSbTNsVQ1opjG7qi3GDzy54pG3ijM1pTJfgQqR0KPOKhBCRL16IobvDtKuezbRO2I49rvVaP/WRfVnr4eL1AiNkaoR7wgrInVvFlzeG7WvHlIkuCoHd+ORiX9DqQ2J9RSa++MUaWqbmjSoFTY5Y4HnQINZnvtMIq6a5FMqfT/mn5pRfoNKdItV1HpSGmRNEq9e/MZtNmxrLfasKaLkNmpWipbu1hiZDm66JFHXEPkMfbYj/W91aX851p/FMmRzvl/dxvoUWSPnoVs7VmrRk97d/Q0h7TrBTysRuCyPUNmNcNHs+YapZYOoyaNjceWasS71SiotTmxU4EblrYDj3tljA6hfj6umsNNni+z+sFDogqk+xXWocFDBFTQ/r8XQLoWQMohSBjZMF3jcHZLAJIlSZ8/GyIEm0N6++Z+b90RIX+kuiLeZoc7aB1Hj8CTUSNY5hUAQpQeu9XKkWXIPt0g5Oj5kKPb3DCtwSi22c/+Xnijp9EbPX19BBIvCGsgQLj4lT4DbbregYQlSGeOIOdvc4q8e5Gl6KQTlL3eu5P4D5cD3LWOHzgUsRA5mFIfHd4G828eIkdJBuumrQLpqmtesAi9YPUIpeg3ZIsCfpQkXdU7OEYkBKl3oEE+pHmGVJ7Ww6uRbIhcevEvpRhhyg+XwQiskYXoMcsRitENUIG8VfdDJEY2rt1m5MgGNdKj3zNGhiH7QfbICJILamSOkgsxakYy5FBqRiR5NWrmUv7Wrf0tRFiMWI/an6C0aK+MUY0qfuuHeS584H9IXwYJigY7ilQbxGuS+CUJ0v2i+3/NNWHSBjjdctVvfpPyRQmUMmOaPyurN7trWX6UHz1Tt1yTJEGyJr2RE6QLH2EtonytL2VqAqIbbnge9ehFVYP8LXkjx9HVv3/paidGZLOfBpIwqJnr5qcDNerPaoQceQVqrt6SW2KSKOfHqOYrR+6OIfXq1T+xQBujfbFFmOd3v+N/fCIvZouiuDZYvsQuyf4hOP3v6pGqjISh7QVz3WKvFwxAV91S2nIgYdCK/XV4ayzdEGF61G5sd2/snsJka3NEHttkajZZA5Aeu9VefdFjn6vVKNaj2266zSVrzh793ukRRLVnCaPrrieQrMN++uYMQeqfjGgKJhHX2Byxx27jXLY9kLb+SdoY7c2paodUE1pkbdGDg2WOP1iKT9QIcXUlbP4rpuATW9l++X6X/fdzgvQChzbL0i1Gkn7zguUoU43+xE9EUg+hRURU+kiIaH7Ne+0eEiTBUc8gqtnHI0TSYxc+4gXJgETJmuToxhueZ3v0inDZGNUIo5dQjwgj47O9GgFGJEjXZwW1QI40Q9ejIgmb7aMaCFIbX8g2HNUPjNHe1B1xOyPnaMcP+8ODjh0CJXDV+EuDBjFFcYHbJ3hD6Ldar31twJGVoxfgAlGyDBmQXpDg/OkWfPzpFv0MJPXoEUU2H9eSENGZEDLrdyAF5SMpR0aNYFrEgPSYSdaglG3XrRzUQoxksvYim+ybQjV6KeDoWc7Wnr5ORLWbs0Hq37yURSS1DOToFLgHrZsXsX19pUV7cxB/lVpSi45/4MFBQzQVQ9SU2iAYCpGlImePVEhThUnzdsB9D99DexwlSde4sPaClSSA6AVg5U8sQahCf7qFL/vek9QjmOkPio7hwJH0R0GjX8/TXqxLkI9cxmJkUXLeyJD02K1XeHOUI1nDWjZxdLX1RwDSS95lI0dE0tMc2JCksmNkFYnTNeTImiOXrLXx82tt6tkZ/n0yRjWcLQKWCu74Hw0PvvFOe5AXpsGujDTIJW6iWCmrlHZk2yRtpEgqsL0gQDL26AUnSI4axMYh9Cf7AVQBSZjZu7pRj8y41rOHLB25rD/aje0r2Rc5h/0I2aNHbOHIRLXHaPVFfxTJ0fMQ1J4Hk22i2ivSHBkt8kn/770cYa727NMsSE9jZLPp2tNgkPZCjtggkRyhGpE9ojvQGooanKiTtLLvJ7J7iLwWFXZ99H+UzihMInOtaQnFSNmpwcZrD/JeW0c2S9ILpEahHqEK8ZP4BFAycfAqVCCVsfXIDmlks4VLCiazfc5vo9olIlvDshGGNgpr4I76Koxe9Un/824qG0G6CXN+5Ajt0UuA0bOI0bOsR1aMzINAejqbo/6So/7iAet6kiQvR+3ovqGnsM22YtTgpE5Ki8p+gvqRqqFfc9gf/ifsdQwW4hPolMvxZRvEV42CDM6C9KDv/RNIVo2MQWKPZFHii9TIQUPo/OkW8d6+ZZtEKPXINNmudNTDpWyBFMlttM5kX4IW2zH0mL0wVXPJmufIqRFiZNToRYhqr5A5uu13AiNYL6EeAUcWo6clRrhKk6P+zVsoEeoPF+uRSNa8x3Yd2jb1awf2usx1xyM6y+8/HnZ/JDobDMxgnsUeHPySrzuqLC5camLbC5JByUHEHLGxRmA8SYKfiCTpsjMzNT12JBr+lwZRrafO+VGNECQb1h5RIHmOvBzdSMnai+Yij22nsoUaPfmkdNmIkSXpaeYIXTZhlETJyRGQZD5poXFytUhM1hobjFyfH7aGnFK/rq4YlX2jdVWlZcff8eCDQnkGC14SNUjiZrB0SUKCRKxTlW9D0oA7HyZBIogsR1cRQ+bJCtGfxENrUca6xZcl436arhw5nFwFqUeQ9/cUcnQJPB7xGLEaXciKJChCj/0qqtHzz7uo9goHNRvWbiOMrBw9SWHtJRXVnnVy5EhCiJ7OkKP+cWTzsc25bDRHSo7sLO1Juu5YZmNUTVP06P8Ia6x69oPF2ONgl5SpFH9QEAGFTg1SHFq7fi9t2EZBegEFyakRgPSC5ecFrUXp9Zq5YBlJ6sGxLZnz9xQ2u4dWI+GORDH7l5ewObqE2/xOkchj3/qYAulVlfM/j1L0okUJSIIda06OpD166fcSpKcFRxjUMuUIOWrRvH+L5i1AkFqgIPWn2CbkiDACNTqFw1qDgKJqZTXX1dWcYze7KVaUijxMQbfDOaNg36MTobChK4MdbPwfNGQAzZBIPTKRjVFCjl6wHJXCz2v88hpJklChXGVIP76mMbpUBLae2Oe/BEl65BF8WDVy9gg5UnJEQe15ztawAvkKuOxXsEMLHAUYkR7ZB0Q2ZY+u91Ht6SRGjpkWlieIby1Yjfo7jqw5YjVik92gtrbXZbybQ40jNUXDaGv14NgiBxUiEdyiWpEcqBX1ggCzQSZno47tNf1IixgklCOD0AvA0AuZ+LyG716j59fscpLUI7P+6L02xTNfArhUZv09qT97EUa1SzioXchxzSf9oEgepFtfZXeEce1FbK29guYIo5oPa0+K2hGWj571BknoUQ6QgCHQov721aDUHy5lkBCjpk1akTei0f4GdfSMUfUjy7S3sYY214V6a3WUaPlfypoWCdTIhUFZhPQVTJP8D/3j/WCRrvEYCY6u+hOgFOkR44PkMEH8Fki65ap4BlIn/S5Tc901df4Rp2s9yWQDQpdc5tTI+SMvSLeqoPYquiOQoxupRWsECTlCOXrSLZCjl8zCmGbVSMS1fEACl22eDD7Aj8GpBb4Kv81y1BhuqW7UCNxR/fbaXnc+omwUVVVSVvPRxx90HtlRFFCTNaAmZrGVKPkULgp3uPF/6J32uLYrg6jmllWiFwKGhBahAiE7ev3pT1elD4QIy9neGyX20VqM7OFrqEWGI88QkSTVSCVrr97AgQ1c9ouUrL1CWT+B9DsR14K0HwObTfwlRggSGO2nQ476N8cnI0dIU39yS+C0ryc5atnUmyPgKLDXncp26EMFFdBq1fzDg8lefVRzFDmXb5ulyJJ/gNgPqdTOKJKx2rdfc00Y1RxGKEh/YgNEmuMEKMnQa6/92f7KVb1zO6MeeogtOI2N60c9L2WMLiGXnRIjlfNbOXqVOLKC5JJ+kCNUJBPVbnuSXTbkalaQwGezy2ZBIk16WpntmCOCCPhpjiENlalFCylHJqoBRjguYlZtba/LduhDJe2qOv/hf4bELsaNog1W3oi3xwaghQVs76oGuT8Yf79vptjQdu9dtzuM7LpGcWSv115AjP6UYuhPIUF/ds9X9c6d9IdpW49gOBvcUc+LEaOEICmOXlfJmsXIcXTj889jsnbTi2Syw7iGYc0OjLAeoRo9+/SzccImkranJUbN+zM7jBKJE9eTyB01ITWyHLWuo0Eo0w7ZGoeob+40/A8PypwscDkKFL871qExOJSwKIDJMW7fk+MkbsBfHr7HchRqEYc1g88LIUEpBQJ2zBM929cXSlMkQVCPHtGU/xuA0S8vY48dQGQYekRGNdlZe5VjGqqRzftfvJFK2Q4jDxJUj6wegRwJf0QgXfd0IvNXILmwRhj1d2rkuyVkskmNDEYN2mt73aks9/+scLimyJprITvSF4fhSs4XOcx0ABzsh7IVg0ySkjvky0S2awikiKTXXrAGKacdQu1BgsSFIJm0rWcpJPkdbH6I7Q3zuNQ8emJj7ZJLEKNHLpH2SCf9rxuM3gQtsk+vvurc0Y0U14wiUc4PYc3VIL0/Aowip/3s008HDkna7ad1VPPweFWCX0F3ZM1RO5OqYV+t/YmaosPLENIqVFF5fqc7hj6oQ1kw4RFoDE+zDZYUydNFdL3JqZja/C9m4WzuP+AvENleiBEyH6+98JpIzBIKJACKFpgkB1LcJOnpI5z22W+AGKHJtrkaVY4euURjJPXozcdcTDO5mnNHz1NUg7AmXbYoQj5pGQKD5JoigBGRFDFEwQ3K2iRJYIz6kxL1b+E4cs216wmjpq2oHdKgTdCQLcu8Y0VVuS7obFO0sHwYqo9mhSnKbJWoUCd50+Vx1iwL0h/vuvueJEg2oCFIKSMdqk9I0l/tF194o7eHKH37R5+8IUNWi+zHG1aWfolydAnIkeToMZmsWW/0+q2gRyRHXo8oXXvxRSxlM0e/k/YIykcvXX211KPryCA9m0RJl7b7gxnqTwbJyRG57P4iqnGqdoquO3aqVoYDaILKdec/DPGnhQTtDd0RE1HPzzsOiShK/FFs1PVApIcQJkiMQ7rddvm1EgFIdr0QI/RnH8NSOsQc/dWClN3x1z22Hj3eQIx6vnEpXT3fwJzf2myB0Vv2kUrW3nRy9KpvrSmMkCOzfnvbkx6klzDtFwkbCxLENYptWo8kR/2lGPn3SpLIYzdqDXpUZ++r1wer7yzoQjXHIMNy8jFIWGIX7FzBUcc1PxspGia61BQo1WD8s6E98uDgAfffLq2Ri2gvJBiiYJYOZETPX/9KL+aNA6l3TpTs+zccRPatfb6UMTJPj1wi5QhAegvyNPMw5ojlyIL0KhQh3yY5wrD24otAEYNkOfJy9KStQZoPwgg5uo4qSM4kJZfFCWAR+Ah71Ly/KB2hx27Quo2mqFYZZq8PMdrlrlq1hj/OFEUiFMzmxyZbNtpSyhScxMaFbffd2pnbPtvQvzz8mo5n9yBDEUaZEvRXIshxhBghSKU57Tfg9Q0UIyAIX964+BKOacDROw6hR96yEL2l9ehNxkhna8TRKy+yIFmb/VslR1aQHEc+Y8NlUzbD0bMBPuyznxbn+NM6GW9WI+wRYgRq1Br2WQsY8h8xqnGYDmjDHo+0SGqG2MAYOCffu3WeR21ydKHMv/PUKZsu8kLb9//b3Tau3QNSdI+XohdChFIQ/VVB5ADi9efX3oA+LA1KJkB6o4cVoB4Aj0MJMPrlJWiOHEdMEpeyX38M5eh1skevYvHIBzZXPLLpGvoja7J/S3VIzvqfxALSS0HCBpqELMUoOaREHJMRrgXBRKUjY7JtO6T+iVqM8r+JbMUjJH4FXe4IKFJrkGIKg5WPZsEvBYLl2WFGvC8aJMsAg31shPGRwQP+8vfXrrIgAT8GpHs8QlwXSuqQJihiyIPUKzU84jjqYVHqgSTZhRi9cTGbo0cueYej2lugRW89hopELL35mOPo1Vdv5VTNhzXjjl60xexXXkE1smHttzKwYXvNcgQg+YwNApvDKKlJN2t81GesR+yx69skTWjREXnb60pqRK2g66OPDxqSuQYpJBIb1LSbkqXLqFqZuf9ftt0G450jBvzxH3fbATYnRq+9dvsLr93+mn2gGfozeuvQCP3ZR7AUQv/86z//aX7lnjegH5tYhh/LEUkSEEQM9bRydBkwdIkTo3dk0o9qRBi96eQIl3FHrEbPI0c3UVy7DUC6TfgjMtovQZ8Wrmdl7i8lKWW6I3i8HgmMbFRrcJLqy9eqmre9toeoefwKu/4hH4pYMwbFDIQUqY5JOk6KPFAdqu1d04MPmsBmUjbrje4x1+2kRbe/FsEjA5mzQX9OAkQYGZD+/No9vSOQQIRYi+wFKL3BcmQXRrXLKLBZfN4xckQXm2zbEHndckRFSHbZbLIpqr2oOPrtbb+VGFFc84ENGPq9AEhgFAS42B0JYWre3/dDmrSr01kZo/y3NlZWiX7hcHuIWr5LxLDUlEjwZlDstxVFQqE4+rl93rbvP+Qvd/0ZTfZrL/zZKtHt3g4lDVEWQITQP/mCZSJbeFDtGyRGb/QEhIihnoyQXeyOHsGwxmJkKXIm+3UoZL/5GIH0KsuRzdWIo+dBjQAj8Ee3Ud4vBOklIOklmhwxHFlJCvVIKZJXpSRHbLP7X+/MUZPgGKPqed8q7RBFX83hj5cCTjpHc8WAwSlIUq01h48fgHMZ3GBq8epzkU3qf+8/7oZ0/3YMZUjS7RleKGGnHTsCH7/+/IIfawOk3pCXfcCHXr+8hJfhCKPaW84hWYxed49boXj05pvYocWknzHCqAbp2isOI+uyb1NhjZ026dFLGqOnBUcapv7ZHHHOb9Wosd0HsjdJ2qG1pC8vHJjDW6elSNWlE7XrQKYGO189WP1+Z5P8PLcw4CRUNvX/+59feIEQsiJkBen2lAolGUKArBsCiOCNWH81DqlHL9wh2csFNQdRD9QiH9HeQJNNmdo7l72DQc0ABDXIt5ij17F29OZjb4IevelzNWePSI9uRI5esRzdRPZIR7arWZBgmu2lZzVJT7sXqUfPJuSohUKJpkWCI9Xy3mZ9uNAis4Y9+Mwzz/BP/JkhGSsZ9AZr7zNYsSF1TGZnQ0QQEz23weJX6GnQYErs7oXABlHsdhvJbo8N9Z9zxDCPjhajv5vLCtI9kJ/ZtJ8imoIoEqN72B2Z9Y5RI9IjNkfWHTmQKFV706ZqliHg6Pm3qZYNesTuSPjs3wVG2wY2qiC99NKzeMVLcAQoZUY1zvivtydHnKgLRvnuJqpxhNeiTvaE4kHP4MpfkgJlUpTJurZDzXdvg7KThGtw0G9zUwFQQvrna8jR7RDPbkeU/uqUKKVCLEPMURDQLEJ/t8sIUm9bQjLrDVCi3m/0TkQyx9A9b9yDGD3CcS3AyFBEpSPzgIj25ptvMkfPGzl63i2y2S+CGmFgA3MUBDYGiVL/l2xoeynFkafJPF/fv38ukqAEGfRj8z3esWIVR59N9Lvd8czjjyNFQFJZaBqc62uihSY7sXIPgDdJ7hCbIUN03u8M0oA/QmADBSItujsrJfsna1EyiJEK/d0i9M+/48tfbzfJPTZr33ijF+DS20U0hRBxRO7IatElliKJ0SPMkX3YCuSbVo7e5KhmTPbbN7zqMHoR7BEWj6wcoUP6XcCRMNq4nk2L0tPyKYccsRo1r62TtCp5FowqVXVaZL6tsOujzz333FACaS9FKdQjL0KaIi87VITUB4zYQ7dUj0Q06kzqP/Qv//jrny1HKER3E0d/jkOZzMYiRw0i5CGC9Q/z9s/3YJpm6bFilCVFKEb33OOiGoe1t96hXO2tt95SHD32Jlgj1iOoHb39vLPZz7+IcnTjK68oow0W6d0nM1K2bJAkULlMtn1q3j/YB5JvwQjO3HdaVDj80SeeeuqJJwKQ9h6llEyJWObkRmX4g6OYJl5ci23QoHv/8c8/v/aa9ER3Zxpq1iHN0d+JIVYhXv/4h+XI5mm93wCE6CUEiF4tRxf/EgiCMvY70Fh7B/XoLe6skcm2eoQg3coYgT16XuqRzdZeoWX06Dbns2970pL0LhukJ1+SCyHKJunZZ3O6I4NSPZ2k5XtX9EOqdRKr5vA/PGWX4ShUJIhv+wgTi5KgyAWwKNINHuLtE/XkHE08zzbgb/+0SnS7laG7zYUgRbY6Q4f+LqNZuAxIJmMzQe0NCGq9k8HMXfeAHLEW2cBmo5rVo7feIjlytSPkCNRIhLVX3377Bm2PbOJ/E/qjm1iQFEfvIkdXvxQsr0oJs9S/fw6S+rc9oUDgkPeRD0dW7+TR61Rz4B+eGmEWkGRBeiZa+6ZIgyP7JNyR7O+78OVgEibb/fqgQUOMQXqN60R//bMUo3/quuJfE5HMKZDg6B8WIPtk1j9vt/Y65aldMAOGEKN7XEy75BLI1awavUMY2VyN1OixN0GOQJBefZNqkM/by2H0PMrRi6+QQbrNBzaZ+r9rYHrpXalIlh4Z3PR7+7CwZILU5MQCyUP1I/Oj6DD7TW4dP3DkiBGMUUKRJEr7Q5hk+14OGQ1xFLHnlq1adVaA4egffzVxzRJ0O+Jzd0KGEkKE4SyQon/ICznq1Usr0T3KWAuIWI7eIT3iIuRbZI9sVMNM7TEOa752hDbb6dGLlPe/KAPbTTqyvUsgAUdPphRJCpJ793QOLfp/w6UWdeqU54HFh3eS7B0/bNTo0aNHOJKesBYpCdKQfdel7F9TLlt8WemS0CST+RtBggT/buLon4EdSuRk2lJ7gkiHeP397nt69eilY5n01fD0hsPoMg0SNtbeInf01mPIEcrRrahHLEcQ1N6W9uhGZZC8zXaCBCSZp3cNNe8GDOHTs4omUqWb/f3WA2/03sBCyUOnPAtGR2ktGjZqjFnMEZEEIKUk6RkrSPtDk8L6tnh1vzJIFpvcB8S1wYajv0NYA4LuDqVIcfR3B1JkhZghjZHh6PY3evaQYiTDmdSie25njhAiyPnf4UzNqtFbwmW/zpXsN9lkv21AevttEdY0R5D7RyABS++CSXo3hEn4JJfAmXdPZ4S1O8ZaisQ6Kj+KjpBaVKvwAaDIgsSS9JQAKRnc9jmBUxR5m6QttxOpQcJhD+bzuIxBMhxB1REQuvufdxty7o7TehnE/hmY6ZQQ/eNv//ibWRTYQjG6x4uRxIjcEQY1jmqPEEbGHb31OkS1N40goRqxNwKOjD16W9kjqGf7sPaK4Ojd2wRGdmVwhNIknjGqsSY5iIrGDiyoJbUozzNCq0gtqtXpgVEjx3iOkKSn2GsDSBmitF+WGoJTM9qDtQt3Q7p+0BtabOitASTgKGy6/t3VhwIp+sffnRPSCOErcHSPNNZKizxHt9/zmuCI9cgma2+JhsjrLq7ZC/0Rc/S29UcxRzLxDzh6lzmy10tAEl5Jjp6lSHe9ECO68fr/K/7X2GEyNNXqVCUviCpUUVrU/Y5RI80SII3wIHmOkijtD4oSdmkw8TV4iNgFINDyzZFBgwb/8e9/ZYL+GouRyspSkSwi6G+oRU6P3lDJGcEj45l53H777b+8hGtHTo3e4aCGcU1ENcj5hR4BRm/HIKkCkgbJYvSul6MkSO8GPIEc3Sy16P/9q8hQ1FlpUX5b0ipWlVpU0BUpEiRps50bpCFD8oZpcH6/zaE1aIhI3vi8NrbZg/jXzKd//Mc/HUjJGnWUlaVMdYQQrr/fTtFMcCTDGWIkOOJcDYtHDiSIa7CMFjFIbwqQLEdB2q/ytRAkI0igSUjSS+8qSXo3GeOEHGFg+3+PFv3rX2Pf7yK0qFOe90SvVFVqkaTIK9KYMYFD8iClNUkpk39XBnJyAujH+1X9mweTbEUbZz+sFt0dFBjjUJaIZP9wCEmG7GfAkSMn1CJiCDG6TNsjwZHVordYi2zK75J+wdGrXo48Rq+kOXoXryedHpEkeZQETwKp670c3fzsHQYiQ1FXVTDKrwVycDWlRcMfHT1u5MhxMUmjVfKvSXomvfZT9pZiUB85oTonmLBB4+NhVqGHXa8sysxSnvpvXoU0Q3/721+Ao14iOYulCCAiOXoH4hqtt+zDRzVrsgVJb2o5grD2/Ntvx3IkQfJ6RKHtNuG0Q5Jewod7b56e9abojiKkSBWMah2d18xs5WpKi4Y/OmacXQqkkQwSBDey2s+Vokh7Y5YG5/uLgwbrpE6dHAEjSH+zBsnyQyz9PemoMwx1SoUMQHD95S8Y17wKvRGEM4LIYPSO8NjU6A8xgmzNi5FVozfJHVmD9PzbCY4ynLbXpHdjjpgmIU0A1bMsRwjRv8Z+MFxpUX6NtEOUFhUOJIoClHzSJopIVpAMSkMdSI/n4EjylIXW4LxD3qD0t7oZt0GQ+JMxUjVGD9I/vKv+u2Ao4YYUQn+xTw/frkqOobv2HPl2CKnRW+884jgiNaL1ppOjN53LfvVtoUcysGmO0iTp9VLw8hL78KeFEpn1gS5e59dIO7JaJyFGhQM/HDne8DPegeRIiiKbA+lxB1IGRmGhu2wClfcaJLexmXxtwN/+DtHM4PNwnJhlWOpYhv7iL2QIObrnHmmQECEkyDF0+923344MuWyNQEpx9Kbj6FUX195+GzF625ez03oUcpQESXHEHvzdd5+93ub4BNG/PtJadMyR+VB06DGqpT/sw3HjYQlBihRJVrVZkRij7NgmvNJelAUG52vOfZ1y0KABfwFTbRl6uNRo9jcNkofIPnl+cP3R6tEbsRjJgIYc/dKNHFFjDThyhezX33rr9ddjOXI2m6pHb8f52iuqpH1bSFKGJCmOaD379P/7l1sfDVS+6Ji8Jq8Pq56mKEApD5BKt9uBQBFUiBR/5OYjeKt6bDFv5mvAUYYMRY4owwull+Xonjfe0EqE0eweh5C57r79Hc7VhBqxPXrM+6PHBEavKoxIkWJ/lMnRuz6yZUmShOrfRZIi1QKpnlc79vDOIUUTJkyQIAFLIz1JY0KQnnAWSdrtvFCKodrroJf2VMDRP/4ZVIdyFYgCGfLBTAEEV8jRPTJB8yDdzcnaOwFIHNMw6w/16FXpj2glOFIghYr0bj4oGYj+JSj6l6aoc17t2JCiieMNRHwFkqQVyTttBCkmqUwcJU15/jDlCHeWI0PPXY6hHHlZylAnACKM/viXf3iOWIxCKbo7xZGFyHP0Oqdr0h0JQWKXnc77MzB614OUi6WX3r1DQbRXFB3VKaZowoSPDUIfR4o0LlSkMLIxSUP3C0bCSO3LsnXIv/wDALrLh7KM+lBOhpwG/dEv9ke3B8UiMtcA0d0U1oQYMUnOZb9eqj2idC0FkiYpNttSlCKcAoj+tTdN/SM0RQ98MkGthCQFFklXI58bup8liYGKpKkMMjV40BDiyMBz1z8UQ6XI0N9iFdLr3nv/+I+744ojMcQQmfXLd6QeYaamo9pbQo28Hr3pkv5XFUdB/UjnbJHZ9gUAfvCnKpzZt8M0RXk19UOKJkz4RDIEJmlCbLg9SKPHSIf0xHMps71PJA0JAp2zT86VlxLasL59798cQzoxi3L7v/wtZYVSCGmObo/ys7slRwk5EvYIIHorKUcq7SejbUh68e0cBolBUuHt3TDQvfvuK3doIdpLiqrEFH0yQQvSx1KQmCNdRxqtBSkKbXsnSUNy/RIHPMdTqcbp3r8lzdA/kuWhKJIhQoKje+2FT8SRKjjefbvWoocfBo7eueQd1RGRYU0ENVWDfPPN0GW/7fQoF0hIEYD0ioLpXX568aOx/yqForxGQ0KKxn9iKfokQmnChEiStNceE4LkMSpbDaBsoA1xb4YMcV/JUicT12Dd7yHKbHMkZChA6I+M0L3A0e33JLWIGEKOfhmZI8kRBrakOwr1iK12QpASkgQsveJhegVpelJDhKo0dn9R9AlRFJPkPdL42GuPsKNIburfgfT4PpUA9h6upCL90ejR/Sg/+dqhwE6zCgE7cv3lYZwJEQgJMXrYXg9HYe2t0GS/ntYj5OhtlfW/nZKjOLJ5TUKGfJD790eREBXtH4omfCKX/SxTkkKH5GaRnvIWKRHZvFk64EglquQDTHZu6LkfHgmEwjJ1JEMcyO4NORrwx78pjoKABhQZjv79jpwWIYxUa03J0euaI4ERxjXzHOpRkiQH0ivvYpR78pV//yu19g9FE83KTZKwSSmQRBFJgZQg6csSJ2XCDUd/+9vfshAKCNIydG+GDAFE9w4Y8Mf7H+agdnfAEEHEHL3zTtplvx5zFGAUcZSoREbZ/20qvIEavf2/sQ7ZNWm/RDSkyHxoloLoNp6Dm7LaQpCeYpCeyKlIXxZGzwR6VDpCqeoQeqEEPmbh8x/vv9tViiRHDzsxeviuh+9W7dkwrIXJ2utxuiZAopTtRXu9GDmk0G27YRIjRGNjguyavDcUVU1RxEvqkX1M+CQZ3CJBck5bKBK77WxJsjAdIKCGkCSxX7rXcnTn3+78S0anLFUaikVoABBEL7gGPfjHu+5OaZGTIoPRXQ//+x2tR6E9Clq0AUfKHrEgvZgGSQ6RMEy33fZiAJGPZ/+aElCU152sUxRNJYboOVCkTxIeKUOQHEhB9p+LJBoM0AK1v+RqiLNJ9/KIB3yk+2RxXi/NEDMULMPRwy6m6QxNcxSka9odvf5Whs1+GyNbqEaWpheBpGRkC0D693v/ylz7haIJU6dOnajXJ7lAmqAskkz+w8j2hPJIuXxSQqS4domUMVtpwPIbKTBh7S9/uROouS+pQonSkGdIK5Be9hZvt3uEsFjkGbrLXhkcydqR7K1pl/32m8moZkGKOYrC220v/m8OiMbuPUWd+YEUTZ0IHyFLKZQ+1vl/aLVHRA5JhDZ85M9STsI8PPnJFtqjAKH7qDikw5nPygIdCtaD5jHUMDR08AB7V+6oWvSw0KK7gCM9L5IgSQmS9tlvvh2A9LaJaM+/nS1IL5Ir+vd77/0Lr9SaNO2BmhKiznlR9L2wjzaV1kTJ0ScU3iYm7PbHkqPxYRVpdLZFAkHaZ4qSXPHjmZwD4UOBIwDnPmWl/xKlZffmDmQPDnhwsOFn8FC4BgwaNNTeA/d2n6B5LbJCRBjddfc70dyRaol4jt4EQZJxzQrS25Ij9Edvo0OKJQk4uu2Vt60QZWrRpMnTZ2iKOn0vzxwNZQg+LEUzp3qQovAWJG8TZBEgI/lXM7Yu+39OggQs7TeennHPz7jhy4yt4UNN1vXH+5Ca++ImRyKxj1VosGHICNAAfxIF3E9pwF/uEiVHIUV3IUlm3X//w/9WHIX2SGX9QfGI2rTRgnwt5OgVntY2QjTFaVEI06RJ02fNnjFMU1SmTL8zadHUmTM9RsTR1IijiUmXxH3bccnIhoIUK5KD6UDokgMr4Z8gYRtwL494RF3WOCuL3dBgvPgeg+KYbsvRwzJBu1u6Ilr3339XyNFb2Vm/0qO3QY3e1GEN8jQLEjCk9OgVy9D/WobeS8ezSe9Nnz1r1qy9p6gzg1TzgYkzLUcapImRSwrrSUTSxyr7H5kR2UTWplDCOoBN4TiNOwBcRSABRw8ROQ/FCP0xlwqZC+KYuGugh2nQoHvvf5gweliKEXN0v8VIc/RWchLyrSRHkK9FPhthogKSSv5NMJuODAFEQo8mmc+MDs3aN4qELwKGgKSpAUqJ5bM3GdpyOqSgro2x7TkpSK4W4OKd8E9Dh+4XtjRKAwwrL5vLrpeRoJfDvN4gFNihIeZj6JCh+thJeTdKu6Hpj3fdfbcOZ3cJLbrfXsSREqN33tHZmoxr0maDP4qjmtUjK0hQ0H7bMTTrPVr/knJk30x6b44haPZsw9GMva1duxTNUDRz5tyZtKZKjrznjo2S0CQ22xNSjf/RIUiSpCC8Pf64U6fHh8q3CNjjBBe+HzrUO6u8ERMcDQBOXobHH+8lguwn2ZZ6qEnFLEN+U6WcHuAv2gMDH1ZFx7seftgzhBBFHCWq2TqsKT2yUU2j9CInbL4z8vb/fhgx5MTIxjKzjBAZiqbtDUXBlNowoMiBNHNmRFEqukmSCKQMizQ6sEjPudD2XGiS5LvHvXViwMQzI8UqxoFRvBtaCkkDCBcnPqq6GISyoZCODUV0BgmK/Pm4vFl38KAhA+705vquh6UW3e8oiv2RStcwW0v7bM7WopiGFgma/v/+3w/Neu/D997zJLl49t682bMwmIEWzdrrWUcnRoXD5s6cOxc/1Jo6MxamWJfClluyiCT0SJQjPUnaKimkHh86NOMX48+kZxf8xb4bYRo64N7EGuAM9b0BQ/aDIpc7FlfeZcBxZeXofsnRXXfdFWN0552GoyCwxRxllI+wEpkKbTCBZBmaG0LEKL03Z9a0adNmTZsNamSkaPbeUHSU2gNSOGyaJchgNJOe4eI1lzxTQFMuklIgyVPadGjzPin03QEaWTw9nvUbOCKyPMWiNNSg8um9o+41T+YxIFmfRoIsRP627frOJ4OIHLEGDRp6310Y0qQtkkpkMbrzzrv+9996O3ZWMVuHNTRHliHN0Yu2fGQZmjmTlCgQo/eMDE2bMWPGtFmzpoES2TVHU9Q5r+n9w8kR4VPhsNlAUeYiuGYG7luzhJ5bVgA8R3h0BJ2tJTmSJCFMwnjvhyWwYjMVqtJQYuVefgpyMihMDyERYsEJNlgO4ofE6MFBJqxFUiQwuhM4uu/O+w1HolEbNWlfzypDYgkpkCFA6MOpliGgSEAEa84089M0EFktgoBmOJr23rC92Y/W3SEEEe0988eaB6xp9pqG70OWUr5pYkTSJx/nCG16Q9sT2iURSsST9N8xV8+VhTXvryJVAj3KWENNTjaUIlm4g1Kco0wguRMDeT1osjVdLSJvzRENxOjO+xikNEbZM2xv46X90f/+74cTp36YwdCcGdYAE0WAEVgjosh5nO55UXRYl86dO8FlH4aiGRgiedl/BOCkScK4lwsll7xpQRoXBzbJkSRpqHiWYA0d+py7mKrnhg716vUc/558+FIgPWND6YAnEgwNBRUCGUrcSym+NWVImgly94n8jLTIS9GdxJED6ZJ4O3aUrr0e2uw3XXvt3xYhZshxxCRNn2GNCf4oZ6AamZ80/LzfG1jIRNirS147rA89Rn5PwcCPZs+ZM3sOIwQQTWM9khzN1PY7y3VTcNOCNC7ZHhEkIUhPRLKUUKpca6h/HcqaloyRXpSeAakyJA14Di7AZ+hQy481Q5nHTtKbQfrOgYM1Rzasiar1/RKjO926z647//ffziOF6drrb2VhZBCCMiQiNDFN0HvT5tof10z0vhB3QIvoR/7ewAJJRH6nPRx5jPiWTkSRVyLSIhIjG+x0aHMJXFgUCO22Byne8h9z9Fxokw7ICgKdi2tA0oChQ4khSMeEG4rXIJGbDcna7W3U6JmHVLnofuGLHEf3EUcgSTDNBizpVn/QFSGSbr3VSJEl6L5P7LTqhx4jl6GZbI1+TpgusRjZJG2aydFmT/vIUCSIyO/kmUOOVlo0/KNpc0CM5gBDiNHsJEZYWPIgqTaunghIRLaRWTnbE2qhIMGLw6qMfA3N+iK7Lum6OWEbMuQZ+5UhQweDBKn1TLiXctCg9M1NErTdex8ao/sDU+S06D4b0ixED5llSTIovQWO+5F3MqfYHkOj/W9DkPle85f8iRAiAsi+Mz+niTCFaCmaShRxREMxmvbRcKVF+Z2CVflo8S1MkVYj1COyRpEWcfYf1JSCngmB9LGIbMkd/yNikJ7wBD2BH+Kz1HriudSvZ/3uoUqUgmqkxSWvY5N08Bo8KOObBg165t474zxfQnTnfaRFBqSXzWKUDEzR4srRvy0/BqCHzF/r+PETJk68zwrRfR/KZQECiZo4capXI4fRLP55W4qEFh2d14l8Bx8ryasFFElr5JwReyMlRt4eTVUQTQwHlLQgpRv/qmEboSQs0xOOJ6FTT+Bbp13uNz3xhPtN7jfkim8RRmpL9zNJhoYEHZCse8k/84wtHt2vrDV6a++KGKKH7MMI0ssvf/rpy6RLiJNY9kv/a77rIfuXOHLceCND90kdQoDM3/7HZn3yfxMn/h9JkRKjadM4pZo146PhtSQRx+Z1OmglTVHXDyiiIUZOiZiiuVKNVEVyKvVws0cBSudInNCeC6RS1nPuCRASX3Rfs3g98Vxs4AVIybm2/OUoAyKzSI7u11p0v4hoTosgrMGyKH06evSnAJSNdfzF0SNGjRo1eswYI0IPPTRhwkOfGB2670PyRFMRn/EWoY//z0BkGDIf/zfVXDOdM8KQRhzNuKNrLalFx+Z1UnHFgKIHMKLNiQx2yl/PnCu7blPDRsnERLr2Cc+QhAMkcWBjlvaWpnyAI5oESZEc5UKoTCd4u/3gg555GYdCEgjdKbToIckRYgPrU4uUXS9/OvLll81j3MvjHnpovGXIKhH8/2r/R4X/TccDQ/+Hl10AEVPkIhr/uGc80FVrUV6nplf4nvyeTt2RIoeRy/XJGFmGlBipjD9ujoSCFBptwZE6ycZwJElSLB0Iqp7TzbzH9U2YhmTdMmdwqTEsVCLu/t6XTvOlKwoZeuhle8llMHp5jOXIXuNeHjnS4OSOKoMD8MaREDFIhiGzptrHzP+bmQhpMx7o3kk65fzu4HBQFUVR52FIUYTRDF8zmuEYwiK2SNR0tjYxTNc+EYn/x2rGVm9CwsCmJYljHAD1FLLk3z71hPrgX4Tf7n7fE6UESW2RPEZ0+GSGIOV1bukzGqJnnnnchrVUgqYhuk8CpCCygjTy5U/HfGofn5q/t0/hQWvkGMuRgWjcx4EU4ZppPxJaNGfGMCxA85Xf3WQOOkJRVAspmq7d9QwX0rQWBdbIJ2phNTsR2GQpMrBI/qacBgJhAABvRklEQVRa+PFUxnoCPzQk8MlTji7+ffLVwcTfGLOkMOJotPc3o0idovPcQ3fqiHbfnT6gPaRtkReih5QQmWuMDW8WpNEjHEKWIZQir0UWoo8lRnD5HohFyKwZw2opLcrzzlZHSYo6Fw5EijIDWuCLdNUoNUUyMbJIn+i4lo5s4kZ/3ill8lTqekK+fSL+FR0nCSR2RxyN9uVwwMTugcc/vS8IaAlrfd9DWo2YIFIjcEgjgaMxYmFIAyka56OZXnMNRSxFzhXN/b9hqgGSX0v/oIO+rwJa4cDZFM/mQI93toIoFqNAiVwdOy4epfK1XIIk74bkhYlB2nuesiF7QiiT54i3i5SepJWRIlskf/nO++/XEN2prLVgKHRFn748Euy1FSOD0KejbUATcsS+yGL0f+P+LwMkJ0Xmxzt35tRPPh5vKJJAfD8/in7Q2bPXuXPBwHnOFUGTV7miuXzJnuzcYJZkpuv6I2MqvIWBzY0h+Wlttbs2IMkL0wiH0wjGypElERuBv2WEf0vPIzJIjEHyUS2Lo0FlBAgpsogOuE+5ojvvS3trB9FDIpqBt+Z8zZuikQIiI0MGoI9jgAw//zfj//AHaZ5nTsWK0scfT9TN2M4/yI+iI4/pLFbB8PemW1tEEM12DDlzLYRohjT34ZrGnsp/j2u2BTsk1a5/p0jBDSOTNJFGPYUqpaIfwUTEwdco88Mv0a+OSNHkI5uzR1Rv3BtBymQIlglrCYweghbIfaEtekiZIrfGgMlmjkZiRKOA9n9jspXImuyJzM/HH08wHx/aZqxfXY7Mj6JDmCKw5gXDP5pOOmSfbbM37unbODpnzjxe5h3ldQohO2owDT5sH06PKQFN8WRksC1yjLjTH7AEHwFCT4WfBl/Kaz31lH8nSaK837vsMvKTKUSeosefexnk6D6V5Gtb9DKIkWRIQWSc9af2L8f5a5mfBa7ar48//oTRgadP4PNHhyuKjsmrjXbQQQcfTQDBqjX8I2vWZ+F+gFnTZP8D+THMTJ4/eb55TJ48bzKDNMf3cGc7gpAezu+mRaVvYkl77eRAGx1mCzcgHV1mRnIDFL2NQHp8bzB6JsdyJ8nh7qhP74simszyfUgLM30BkXl8CrdnZW80kh024DSSfbZY4/3bCYjUJx9PeBQbIDxxlt/t0bAF4letrg/Mok0lrENsiixAk+dPwjV/voXIE6SUiFRo2gwpY7qNO1fGOBaljJxtpMw+RqM2QbcEmNrPUFGEFCBJd5Q/R7khkgyBHN3Hg0XKFd33kJSil0WKpqQIITJ/EeZptKFoNP4tUdUIbitFeT8Ws/FAfEkRQ/Txx3dYivzK71aNBx1UsYoLZxbAbg/wGLcYUJs2yxA0f9KCsWPHLkCC5jmG7KWbJtPEdJtwVOE4AA4EwOtU1iSV/EfHj3qM7P9wo4El826EDXMQ69JIjabLfy5fR49QvyijHOuR5ehx77LziWTPPJMnRlSYGvCQ6OY/FGlRXHJ8WTAEGI3m/6cAI1V99KXs8eOQHwWQVaIJENBMigYNEAxN9qNKXg2Qgw6qcZRkr1P3YTqY2bLmnMmTJi1cuHDRggWWIKtCkyerWGbrS3HTZIbuviUw4jLBXCFJIUgjY5AEUpYkeriMDmIfEkJ/tf6JPkbgN4Kkwf+6I8Z4skYHJDmO8sPomWfKABFtpTOvL9+X2fx4OfDWwJASIxPNPqX/LgGR7oUARB/bMmSgQxM+JoTs+we6dJI8HFUjP4oO+r5kr1PngdOkEs2aM2/SWEvQIhSh+YTRPLJEc+a5hglsirMDc7OCElOMkN+i5HcBUAInYlt8uFYQ3ZQ6kXMagx9jGA58prf4Fzwa6bOfj6bvMxDBTwAt/OhQkihdKzWsPSMO4M4znvlNc088pLuw9wVZvmRI+aLRgBEwNGYE/XcqIfIUGYwwlo1XwewTF9QmDGMU8JFnucik+i5FQ3s9zUE0a858S5BhaOyCBSxEkakWhYFZs4Nat+y9pZRorqp2UyFgYsJqj1P3sU0LU1KrxhA4hBc/09dG02esTPRFHRo9R7JBu3cIBRDRFjlaAx4SaX6U5auIplwRQcTmmm1RCJFlCFr8gSXyGZpx1wOVLeqc19S1XYd4hDpZiuZYhowVmm6iWBEwtMDGMnMJJXKmyASz6SBEaKdkMNN2SE4CzI21iKuTVFDSIIV31hqJf0FjxuSNU9nWaKou8K45vqWAkQxPkQTpGfLTpVEUbqZUEA0d+sTLqfwsskWfBgbbeOvRYK/RJo726szemuIZm6LxCiCUIXxM9OYamMgz0T/ooMpdpBjVGv6eRWjepAULi4qKIJYtWgBrvvNEhNB0J0SzyI/Pik11whClGJo60w25cWFSDZBEGEmayqJOeTLEAXK0ECSSIzF4lE9CVgpFvAWF1qeuDau6+UHvQ2mRtUQkRuYZZFVJUWiKPo4XxTTz8iFQhH0Mu48or3lZu2zByKmRSfU/mjN9/tiiosVFKERmmcQsMtZoiKZTNLMbvGepYDZN5PczQoLEaNJchdFUPKmEkrYUSPKGyCmmxtBTvEZmvC8tJvobd5McqQG2/BF6PE7x1Z5dOyL3+NAnPg3b+YlS0adeiUw8GwGlIhCjMZg2uP9KCmgjWYoYpPExRwDSxEd1op9vueiggyp9TxavO3V9f+zCJUuWLF7MDIEUcYloMrmi6dNto8QqkYpkUBhIzSIpgubywSQyR4t3kihBIpDUfSMzaUrjRZo1kv56/ctIFH+CKyJstLwzRShHe6tCmiDamAmV8gEva18U5mdBsehT9NX2GQvYo3WxSPqiKD1jfmwsA4ru6FpLwNA5v3lZuyoeIaSoc6cuw5YWLylaDBQ5iEIlmm7XHPNB5954IRJlxsQwUpSfBSMl0eEkXpD0bWzc7bXGZXMz0qFTCmDqlcLjyBRGI3xYKwNIj8cIuezscQ0RVDif+/ShsJmvTdGnMtEHMRoNBnsEVz6kLVIUUbFxfEKGUIse6NZJRqYj8iwXHXRQje/LHK1z94FLFy9e7KQIEEIpEmWi6bioyj0r6rclDBGfE8HN/nCkJD26HTskdW/tcS6JLYsyJRiKvo72faQHaYy/UU4U1nJKUJqhxyMpclvnhg596uXsgmPQRBsNU/2jXS7gpEgWHJ0xipN8ckX2SAUrSMO6d+osbPL38y0XYZLmRazzcEXRIuuJFsxXEGE4mz6dEOKANkN1/lmHZsjpWi1Fc6Mjt1xQCze2faIxGj9O3MpW8wT/640kldpbuDxhUpRG0527nnhuaN5y9HhahxJH49BOS3z+NGRIjYTEDI2whUeMZ6EvipO0pBgBUBOHq7JP/imasdeqQtCpm6VoIQY0jGaTfDSbgjpkGfIIzQoKRHF1aK4nKC1CYfHRpWpu338kSI6mBEr+E3KXTrL4Gsm/4B5EXZY2IUcU1iiq5QYpS4Xi47zkZl3ehOnk6OWXozlZlaGNZnsNAS3soYkmmoUobYyojfbJxx92VZXrznmba2Ov1YRRp+4foBRhOOPsLLJEszVEeqdRzJA79IhmIYP5yKm80W6q3wMQ7iLxo5GapPHjMpAKtWqkJwveoGzh5/j/q+BqXJIjzNhIjoY+zvfEfTwPAdIFose1BgX7wO0eTNx5ZhF6KDvLJ5CsK+IcLZQirUXjPlZ9tE+kwf7440d1/+OYvM21sddHK/66v7+ErfUCCmbIkIVoChqiWWJNmyV3iPD0h58kcosn2/DAHPXtWBGQR0bMDPa1RRzFmqTuRzo+J1EZmCnTnnDuTo/IHTlBejwfiHyJ8fHAD6V3iT/1aZChfeo8kS842sI1NmP5I+x/jBw5zonRx65mFJeNrC26o7ui6Oi8zfVBB1Wo0l1TtHTRQh/PVHJGDNmHY2DWDHvZBzLkZ0Dk5stZs+xAtxUz+0fZAaX5rmyArRTbyIXvnTkzcbYtTW1Hd/yL1rjxGWscbGJHwvIDbGTCtsMPyIU1fYjW4zmXOMVbu6EYpAHP4U3lP1U6FI0ViQbIaJwuGk1pGhfFGCNljEKLPRGfDEUfPqAo6n5cfpvRkKLva4oGAkUMEfnqKVMQonlKi3xuP2tuYq/aXJwpmQwjSRAcaTZJlMBpWlLDNHeuTPrVrv9PlCARSRPG517j1BsnXPwXi6/ZNCmQSI+oeJTfDXFSp+W6E7uQmzCgwUbLp172Cdqn/lKjRZ+O8jqk3bVPQN1/iihgJ4rYHw7TJHy/DBTVqKy+t/PwjxYthIo1GWvLEFA0naVIG2uWIT2MNmPabIvP2EUwUmL/JMZnnuJnjhtzU8uiNFPfksRvjhS1SPk63l+4SoMrCRwEwzgFlJMFgiN5Yk1uIYoP30o7Ijqfgia/R+gcP6g3QuF6NGoRD73EPX3nsMdLOYot9vgPh2uKKuef6Bt7rSnq/tECQxFOpHGGP8VQFHoikd7PEpVqE8RmzJo3HwdKxnLZGwmy420CojmI0Dw1bosvpEpzpcuWGKXuigz3IVVsjad7kxBYE0rjalzIkyyXj1N3EvTZ2tBMQRoaxK/HNT8pP+QA4gNOnvo0Mz0DkEaMGPXp6FE0NTVCa9EYVTISY2rhnBqu8R8GIJTBXJuQppK0zp0+wObZAscQQjQlViK0RF6GDEDTps+DmTaX5JEIwaT2PK1EcjwJD7xJ7CWZMVccRCL3s02Ib9QuZIlZklghRk6v8pcq5ZWYIytHbqLWH9ju0HlcHESqDXXSVD8hDjZRJ5x8mjmyD+aaZoeppa+Dmqyq+qDGehQFtUf1WMgxZQhoJqQdqxjs9P7CBU5EgCFD0JQwmgFCsmVmfJCdBRjr5kmw7O0n29ALTdY6hD1dyw82dmfnAinz3IiMNV6/B7Q+FrI13uuVpKs0lhxGo7knEt0UJ65Nu45rMq8PKNLrqU9fTjXQRpmH8UWjrBKNHuFsETI0mrs4I73D5qZsVk/2kzsURd2PLUtAO6jGUV0URcOWLpDhbMqUJEQoRChG1gfNmj55kp0mKVrIrgpaJ/OdEM2X0WyOmwqwD4hhswQ3PHnrvjAXQUqc9JetSvH6OAO08RO0QE3IjH7SIrmsXx+jFYavFEYJIcpYTw0wT59+Gs6mWYbMh/kXGDV6BGE0InTYI2WiJsWIc35F0vgPH1B5fpe8x2WRIr2zsdPAj+YjRJMdRLEnAl/t4plBaGzRkiVLiqiFS4MACybNl2NJwhFNn0ciJE4FmDULZ27pREk3wU3b2xikWJH8pjZ4oEQRWJ+UkSunX1niRE5JhLWn3B3fss9F5pNuo7pQKUfiDDB/+AArR1G9cRRcnwJFvB9B6JGYgnEWW1axMabJNe7DgbrmeGSZKAqq1527fjA5S4lkvRFqRdMAoUl2mAQHksQgAPwhomgphWj6HPeYPRsn3WY7MZrmT5lwb6ah2Z4pMfpEnmUjb/sHHH3Cz/S14ColFHIElDqlJUlzJI/p9rcNCM/hDpKxNEN8YgAeiPIUHoYyQoSzUShFxlWDvTZ6NMqQ5JVIDAgHQU14I9AiidLIR7tqW1Qmc23ESFeva30wZYHMzgxFU0JfzQTNmDZrMiJUtNhBtECHM0TI7T6ag+1cNylJY9v+eVrWMqIEFmnixPiGEeHdkR1X9HYCwuTeTKBfyWONnzAhoUvOHzFGT0SnK9P57ZEVeiJTiJAePEZHHHCC7ijUIXNZgEYBRvAyOqlGjqRxov4II9hqCHtMYK6PLpsUHXSQ2tnYueD9+SwhXolswVpwNAOc0Yxp8zCQ0RCAgQhSM5gEmC/buCxFtmzpikJ2tmTObDdfoueU/FYmupMEbZOE9H9idB/SFEulrwnqPWPzSQ6Bim79PiYXR+nuhjhv0qkPv8hzcp6QL088NSoKaChCFiHjsc3raBXURieimt7eOB53OGIVcvwdhQqDY8sI0UHGXnfv3B0fnbvXGvavyZM8RFOmh80z6II5GSo2QsQUIUIUzSZRjUiYIvqjplNeNnuWG9p2JahZAUMwf8tjuPRVFKQApE8YJjiRN8Al9TXJ0oTgkwm5CwkOJOmyxaHvz2UejZxto9NrFDzRgV/SEJkn+xgFMCFLgTdSijRyZCRHoEeuITLuwwcKOjME5tElz6OL/LL2urt92Mva6ynzY2OterDGDU0xbmjp0uIldgQgGI6ctEC2OlCHpnAz12oQJGbThQxNmxXLECvQDLHTn5q5cNqOvjsb3aydYCJs6CskVfyZhy23Tk1gTxWDpDiiY02fCk78EyD5c9+THihjjUCGECLz3ssRZmijcI0Y5UXJUjQqJUcCJGeOnDuy2drIRwfWQgjwkd+p+8peH8sI2atT1w8iioAhx5GRoSmTigxDRomKFxfLqTZhrMUsgBIimpOElEzGslkKII5gamMba9LsadghmZgkiW8gKY6TDH+Du4NyeK9JFeoIpKQwSZvteyJPRSgpelJa9BQfKhgQxGfnkCCNopdPWYhGoRCZj6fojQ1ro7PEKNy1T0fQkj8ab2zR8E6SgvxnrqUxIiHqDnXHKQzR9LhUZNe8SUVLl5YYhAxGIEULF4twxk2PyW60bYqHaI77wyRCsyIRYvWREMllOZo7NclRghmFUhq9T1i2ssPehMRIgXdHo8t4VrdD54lIg0bos7pGPMWqBBh5d+3WU2yQRmg1Gp3as+/k6GMX1z4eP/oOBVHnstUcYR3X3Ue07p0LhgU5viJo1qwpY40MLSs2ENmPXGPanN5PmSKUaJadcZutiga8oXuaD2D0JqJo7gx3BzfnkKZO3H/rk6y8TxalOLSNjziSdw/IxZI30E+EEkTndUWHmFhWRjFHdg0YMWoAsPMUfYyCSvao0RmCNJL3Foku/8cU1saNe6BQ2Jru3Y8rM0QHfb+LxLCWMUZBPPPFoulTJi1ZUlKyZNkyA1HxYmOLKMknJZpEydlkgdF0H85m2Y/ZCkoXzWYJdKa5ECbo8Qz5OwGiQdqfHGmmMsoIsRyNDO6LE6kSH/eX1B86OZAezE9wpBexAgHsUy9DA0apNRrUaFSCIzHgMk77o/HjPhxW0NmFo+6du+S9Rd8ve2siL0edhn80RednCJFFYIpN7ZfBKl7GGMlCkXJEPO4vIMJQJib/fYI/I97hH4iQZ2iuA4mcdm6OpsaX/CRDlD4R4S6GSWEksrUIJAVVGqGnxHluSok8QVCiNs9POYgApAEAEtOFYQ3JSbtsD5HqiYyEDY1eSo4uw+R+hr3u9r6jaIoPRJYh46lLLEHw5AIaQDSWWrhKiTRDkcOaxqZoFm/Mnpblg3h2iea6aTYXWYLmiCRjIn2k4JnKT3xflfCOXFOTRAVFqQkRSK5HWypJCh1xIuVTET6j6WQmPH9iFF+OGVChJ0YFWsSKNCqOab4AOVLtdBg/5o5unZQtKru5PqjGcS4mQq4/bMoUp0WeoymTxhYZfJabx3JUI+OtMZ7hQBKXiQRD8/y3zw4suqwy2gEBcaj23MTRa4gPbTHB38VfJY4UB1MlSDjmNnGqGnsTQPFvmSpJTEqUqDtNCPWIJ9iCsyfT+JD/ydQegmgEDTL6Qy0pjGFqxizJoIbmCECSchTM0aq9MePHjRlmK9fdHQbHld1c21OMuour1sBJUzhDcxRMmTx24ZKlhqAVK5avsCgts0pUtLjIQySFaArI2bzpyhCpoQDUoWBIiQpC0wLc1BylRsvvFojmtSdGb8q8JqaywMy45mf8U4d1y/RrBB9wm2JotH/jMOBNiyO4PBTKUeiOyGiHgjRSTtJ6jj4eWKBStL2wRXjUrKg7GmPkfZGFyTAxaWGxFaEVuJZiRLNla8zOxo5V0WwK53hTRERUMuR7cUAOOPfptrbEh0f6BafdwBzttGkxTDNcmJubGPxPLbnlLbjfRIzhxOiegU6VJEgBRmNGxyfikjLlVCDEj8GhXR2jx4wQs9UjsN0xwuZoiNATQVyjrH/0KImQzPnD87HGjVO2qHP3fA+XzTJGYK+NMWIpmgIUTZ40dnFxyTLLz0pzLQctMua6yO06YogmS4isFk3xSqT1xSZlswAfQ8/kSZPspoEiXDTqxmusuazajbVAzZsDNccER27nSQ50cOet3xs31d8tIHUDCj33PTUQpRAj0aPl80akTcrpnkew//G8jMEtHSP4xC7/B45mOVIC9EQgRT6yBbUjOStC+/Qe6NpJALBXtuiggyocJZI0swZyRHNCtHiZZWjVqhWrLUrLlxFFHM8CS8QIBVI03RuiWdZNz7L4jLXcLEZ4qJ/rxwL0gtO47JvJ82ZPSx5by8fa0E7tmVPVmTZTg42VU4PXHHfpmpoCKbipiUjXxBb/Uo9XHu1dkDslls91oyGPgKPRaI84SRsQWiPBkohqY6K2mpOjkcM6d5bR6KgyTcu69QNljDoN/9cUv8wPeuGSYgPRqtWrVq82IC1HjGyChif2aYjmMURTfFoWzCbZXu68+XY4EqdK5GySjY60LRdGbifNn+/PlLSL45xRpRlzZ87MJknt255Z9qUO5vIshW1gx5Hs0apTRwJV0v6ZZ+5HOFwghvHgq6dRcTTaWyMnRU+EeqSDmvRGoh1iSfp4eC0FQJ53bghX5e4qpHX9aDLJ0BSTmS0sKi5ZbnTIUGSfjBaRFGE8C5UoLDYF/RMgyCrQEhgqKeLghQhxIw7rlwtCggKSTHyblk1S1nKnJPsDucQ5FMmjBFIhToMU2ezwhDcfj3wazxoxeswIJmjMmGA2SP0ZgqQRo0dlrqe8GJEk5WiIGI4e7dpJ5Ojdu+d9iFpgjI7uLlbnbsMmCSEyjmj58lWr1thl5GgVUFQMtsi6FpzSljtGVKkJdYhIMjYIBrSXLFkKh20tXigGk0CIHEaTJvkNJGLyls4nnTyZWJqcS5K0NInPiBc6EWeuf+eeo5MpUreiTIAk7FGA0mh/JqkIWPpAXPc7x+SF0qcJgp4Q9UdO1hLdWWmPPn6ga2f58z96r2yRNUbdYbCEKOo+HCiabBiyEJkQhgytWWOkyFG0OAHRFLlnxBlrp0WA0FI7DYDzJHg8CUNkCMLtlLiTVuAznwaVjLueT5edfZsMJGFs0yQJZOaG+AS/a+bc4Pv4N7lDlzJVKQxskT2Kg9sYPimZT7YdPca7nzHqIwsi0rVROcRIBrXRkqSUFI35eDj+7BmAvbRFJtd3AHWG54+AIYhmywREBqNV7IpAihZFELEtV/FsOs0C2JmkpTwJYBnyg7YIkWeIRgN4PmA+yRB2Vvz+SIuSBQl3Zs8NSJqZBkb/viz9En9IdO5bFklpjkamT5vMrTm51IjlKIOkpxRJXozGKDVy/24f0g+df/57lefbVbGLFqP3/zVlkhUiC5HxQmvWrF27Zu2adRaiVcuXlywrsQlaEeRnHiJ2RJIhPh1i+vTJYxEhHErCCUkJ0YIFOGtr945QNJvESsR9ORybpNlJv6nEAj9pHghSKC4CptLJKQ0qpUkzI5AiPZInvsdOKWuNdC8j3cWfhTZrdGlaNCoqPo72/xSSojv4J48vXcpwWEhA0bGOR5iYHGgoGmsssFWiVZYhu8AXGYqw/QG5OVRxtBLp/htNlsBYW8kSGCfheZLFRYu9DvFIAE4FOEs02bkh0qF5anuteD950vzJc6aJ+/ztCzP5khSAFNePEseOjCyFIKYnPjEw+Na8QAqc0RjtsO0/YfQfhumAlt/9z9PGqIvEsXu3j6aMXWQgKlkG0WwtYbTOJGm2i1ZiWID8XFE0OYshO+VvVAgsOY22BRP/do1FhoAitaeWF51co0GSRJlfmzMDffTMpC5lLWrP8ezAXNkATsa5mKRQj/TmbA2D8EvuMFLWHCwti2NLI5YCjEbl4mg0pWg55mhHfdi1uw9o+2KL7PHpDiH73OkDG86WlLASrSeMVtl6ETTRlpAU2Yry/EmaIR3RbCgrccMkNp4V23kSP6vtDBEOlsg9SGJOifYjJQmaw5HNcjTb5V+lM8RtuGhOIBonmBGQFGtSwh4FZ48oWQl1Rr/mXvoWhKNKjWtJjlgXH63lvYwd3N/LPB/E6BgpRd07DVu6qHiJD2cb7NO6NUhRSUmJp8hm25MnZcSzWdOnzAdHjeMkxUtBjDCcOSWyMiRq1TKplxBNdjFsirtLJO7UxreEEnA0jWpCOX00w6EnmDJHm7Q2cQKXAklzJARpn84rzcBoTO6g5qyRR0jL0eiPH+jUnY2MXWU75kGvGt/r4kTNPoZ/sMik+KtIiTaYC4wRRDQDEdScWYomyfRsulQjy1DJ0uV+JmmJHSdZLDdB8rS2zMwm+9yeTTVrEBzIBeOTrEJ2Xwns2nYkmd8KW7Jn+sxsZgoiImiuH3pzwExzz+LMSiIpOAw+BdInAUjiEJuyg5T9+5U/yumL1Cy2Guof9ejwzl49jBR9b2+mQpiiI4+Rhafund5fvIwh2rDeLPO60VJkbFFJCbXQxlIr3xcbp7iat0HI7jYqsTJkrZR9tdMkxUU8UEL16rFj3Q622A/N48x+iiMINkfS9JsVoul0N4l5uGVyHhz9h1VtB1JSiGiuJNwX4Ee/Z+sJXnWgd6BKAqRE/Sh1hk2+Jyn7g3LpxEl/0zhWpFIpCrJ9eYbomFF31HKO2LJU1g36emH52m1sqzVspaGIlAgWaRFSJDM0DGc+y5/C0yRj7WTk8hXmO5bDYFvJMjRFRc5Zj4VevWu7zk9HM1vJ9B5o+jzpu+bIE2x40e+k1D9BkjvWfYaevnXQ+N247naD8baUWJGm8q0nk3oUBjhJiKdDwEW/S46UjRw3LiFI2eUj7KYpY6RCmgloBdLKdN7bwjWtY2XFqHun4R+QJ9qwadOGDeaxEShabm1R8RJJkXVFoRJB/81AZFu3K+1c21KrRhDQ5FbasW68bVLCU4uz10iGLDdOiHBfEpI03R9kQye04TFJ01QVWzoiHJOcq8OY24JL03Iz3JPYKqfO1E3ENuYonGNLipJjg48jZXr8L6SWg4lunzM6w2e7qUea6g8YGmMytIFai8q8tVqv7x8j/7DO3d83FIESbd68eYMxRpiiQclxSXGRz9AmiQRNDAKYDG/pchhIopkkHtUWpmisH7R1E27zdV42z7lpIEftjoTN/rzh1h1kM2c66xJwFHXZ0FTz4K2MYwoXOmCCLnc7XHlTXOeXQkVKcTQhcX7buGxG8lzSIEUcsbMOx7Bl1WjUHV06Syk6Zq/GHP2qLDqy5g/tNHDVmnVGiYwObd68CSIaBjTy1gtFQJs0RVM0adLYoiUl0H9bZceSsGlik7QlVGvEm2Y5R8SnhypDNA9OJZ1CIE2ZRwKkNwTMoY/ZMr5NZ5JoRlKDNMONa6tIJmXH40OncvHRXLPTJIUgpXx2iiQ6d2HcOH2CZJ6HdY90Z02iQfo0KiA5jkaPlnNGfupk9BgMaE49jt6HPN+uCt9TCZ8JaattPLNSZClytggwkq5IpfmgQ0aILEOrVq6GUZJVNEyyDLN8YYngrCynRJMcQ1NAh6YQRTBKG24mwULCHNfwnT4bQLIHRIoTttBuz1ZDbVQUErZ6Grtph4hVH4KIGeJnoiq6C1NOjuSu2vEZJ3WPw0OTeYuYQsqfVpp5/O0Y3+53IyGjonZs4IxMQCtQva/v7UOejyGtix4P+c9qhshitNZrUXGx9NasRcgR9nCXlBjacCRp9UqcjlwuZmwXLtSOaJL2Q3g0MkJEFE1PrFn8hNtL4CdMRyJZVZqtDz+ePc39pMNIFgaxabO9BmUcNomPCCUJUtAY0duzs06QJGYYFnx1x9+O00UDF/y8QYJ7NzqM3ORsIEYeo1F36KGQvdnOmB3SrBgN3LjWRjNcRNEKoEgUi0iLaN8HzQFYiFbBRBKMtllvVMJzAEVeiJQjcpqmdG2eO2p7igYoGJ/k/ZKz8IZbdOzxdJ+5zUOStDfWVkiHr/yWj24apLQeJQ6NmKDkSAkTUeX54hc67X18CBL6bDt35EpIoyMtkjdbNr97WC31Q9/XgHbQQRW/pyka/l9PERYdbeHaaMqSJdpbI0ZTJgFDVogMRKtX2zmSdVDspkFt2DWy2CuRP6HGzWvzhNI8RGgezQfMizVoOh5j4o4zEdu26WSkOXR+dnQku4hHpDy51xw8rWsOvnFHc0uWJEkqZws4ihRJHmUzIaFM/Dwu8WvjwgKUC2yWI9fJjzfuj/a+6MPhmqLv7XUn1oc09QeakAYQbdkCSZpL0dhcjx3rQhroyCQnRCUoROsAI/NNK60tKuGRJDXwLw7LmqKFaJ6bMRG72mC/CI0U8e0lwDMpimbRTbXpZzx9+pzUSvJA2OCvu98I792X8VB3fT73tARJST0KVEkcyF22Q7iTpUydr306mguOcjpkjByNGxMEtO77HNCikNZ9+AaEaAv7IrJFKqAZghCjSTSQVMLjtWudFkGlsgQGQliKRJVoslCiyYgPfbheCswn2RP9cVzbryJ7IdN2etb8TrkrEu7Rbm3SbDh3K+ZoTgZfpS3mKaFIMrI5ox1uYoui23hvvN2ZpHJNSH/i7mQi5QgStk8hY/s0qUR+VHfkQA3Rvge0KKR17vrfrQDRFvRFYK7tgJqnyHGEEC00SkQz2jyQhJ0328Atca5o0dgFYxdEvnoKpGayesleyO43srPatq27dOmSpTC0bT+WFCNLi4uwsWdpsnvWkCWMbrPw1NHpYJNUjXt2/tTkok3BlBSkaJItFd2CJA4zOXkwaXDjAF0xGC9LkZj3sztKHEbDejTSju3v54B2UI0faC3qMnDrti2wmCKX5xeJgEZrrJ1qQ18NDNlaN2gRWnIeA8Ap27G+XO1Gk+ZNnjJPVp2QoPlQv4Rly53FfHxbsBbaMYElHGytLOHOyVl0CDIbbipwC5xmB69lRklrUhTZgsAWglTajSj43OTx4r4U/AWtSiKsUYPtZZCjT2OA0FqPGvdAN61FP9iXHhqvg3VHtnvX/25jiJgii0ORt0VjBUNWLtASreVFoyT2u5YVu0Oxx/r92H73kU3x5mmCqAK+dDlMlgBBHqIiHnXjuVte5t8O/g3tVD9swmWDNJ0KAYzMdOl6cqx59AwzAxl3UZozZ85sQZMHKSlIIUp4NPeEVIwT7+WdJ9wXhCxpkKh+NHL06AAlsQPlD8P1z/uYMtzkM0dIO07/qd3/s2WboIjNtZAi5GgsQlRcgrMkGz1ERJGdjuTxyEVjxwa7+h1GzlQjQraHsnz5Utc7KV6y2DyWSAGKGLIBE1myzE6aPJ1OlWBRcvcbIcecQ4WInnnUk+MXdwsKpEphNTuDo5lzAz1KoZR5tK2DJ2XJ9VncSpCMIhlqRhqCRkqE+Hn0uEe7ayk6bj8ENDseElA08L9ZFCFGBJFZi1CJaAxgO461AURki4ppDgClaCy1PSbzbNI83UOZvKBoyfKVy+mMGyNEOCaZA6JF3FexT/S5/QcuMCBN4xLALErfuJ40nTMvPRPgIZo3hwRIT1UKoObpm7x5QfKRTcpRDpD8oba5jnMfH7zjm8MFOZvr1RqArNG2LI0ZzVuZ4GPUH4YFP+59Ggrxq1IQ0rr7kJZFEa0iggjDGQ6S4ITtchyOXEItXP5GGCeaJKrVU/zZW5OKrAjZiRI7C2AN/TI6RbKYISparHVokdsGIBeoEoE0zeVtsKA+aRmyRmm27uL6wQAHkAApNa87R8Y6pUipzD+nIuWUpTRX3jsFHOEkNyrPyNEAElWtzdOoR4Mfdlnv2pC1KhynuiDdO/9nx84dTNEqT5E9x4MX2moH0RrL0HpD0QYf0YCi4iJP0SSuM03m4SS22PagJBPIVq5cDiclgRgtXUZKtHiJ80NgiMTmbKtBPDtJm2wlSCa06XORxG0jLESWJJowoRcvRbkR0iSFILEeRRj5m+FmnfknlSkfoMbLBp3usI3k4dqRn9qe7ZhPofkx/g6doHUpy51ic4a0ypqi7sP/u01R5GyRp8hBVOIgklNtCJEPaNKVc/NkihstmTx/ETCEIyV43JYf/F9sMCrSDC0CGeJdADyAy3u14QNAWrhg0jxVS2KS+CaRcJD7dDmkNM8JUpKbxF4UnEVJgBT57Kl8LJK+p4kUJj50298MwN/FZEL6ljjeIHHqHw5pi52Roz4crl1RlzLd4zOnGB0bitFOjmjrBEVFEUQmCdMQUZlyFeV11MEVrshCNMV+kLmeDFNJS5attN+0chUctkUTJcXorkmJitwJEWilRQRzAC1aMJa3/Nu1CI5EMvm/BgmrSbYwafM3L0Qc3uaxuZ4TEcQTdHKfrqdJWyRttPVOtsS5SBNjSXLntyuM6BTuGCaZsenhWofSyNFuVJYhOnY/SdFBblaNV6eB/93ptcjZoqKcSrRZahEVi5YoKfIRbfKUSXDZRu6ChVAosNXuVXjg1rIVrEVLQIosRQIhaYccPgskPsgQwmXM9qJJoiI5S3ZK4N42vpiEhmj6PLdLIFAg2qM7nzESJM2Zl1CkKLCpmyuHN8dJ3SBH3l2JTif9RCpUrEfjx0cVpJFej0Z9OKxAu6K9PG8mtQ4+2lDZhR/du3f7jzVGnqIloRYVLSSIyFkDRTyQtAr1y9aYPEWTfECbwkENKt9LlsMoCVzQxLVt3KU+Q7MnHdGQmzREtMY6iiY5fsbSJ2OdIhWNNYKEB9u6phvn/3NmB7OSwNEcIUaTHT8eHt5iEG2WiyNbyJEiKXWGZMSRlid3+64JiZZKlPnbfM1vzcWZkC7+h330fikW0QJ/3YUeXYwY7fARDShCKTI4FJnLPtmSsYIIurcb3MajZYEU+VIlNU+AoSK7DRfwWb0KjrdZZSPaUhfRiv0mNs3QWPvwosPnH+HTJLdJCb+2aBGANH3WLBXXzNOc2dNli8TOv82bNye2RJNJhNwWFR7OtIdRzJ+X4mhOnLCpc7akJukDbSZOzOemXd5B6QpTiiM8L8QEtJF2yLGL+0kbb70fITrI7ijq4q/Ow21IY4o4oBUBQVaIFvr0bNUaB9HmDX4IYDm4IuHJPUWTXeWb+28wk7Rm9SrSomW4kw3jWZGmiBiiRZvaYGhpLFEziQGC+48ySWC2sRwJdW2ZsQmKYMg7NEVCgQCk+YjP/Hk47Atn4phndXtTzVGY+Uc2CUTJd0vEYdul30ZJt1TCUqRo1sI2tE7qB132uxDlWhWOlX+4rTzGFC0EHYLLmyIL0XaejZTT/iUuDKpapWjALSxyY20wlYQBbaVRouUwrV1cLPcfydoQDCqNxaDFRx/BSX6Ajzt6bb75woL58x1I1pSPFR5pNoa16Zj0A0Vy66S21LDFwI6zwEiLmxfnvZhOpXII0lxX0PYYzZyqDleeqDO4T8Tt33Lw5G95Kox2eICJVaNRD3RXEHXfj97aru87MYInI0aCoiVSi4rGLizypgilCBja6hpviYCWYIjab4iQFSMb0mymv5xO+YdxAH1AhCVoESIE3V13dOiCBXSQH77Y4yMcTJPmE0djoby9wB7FhpoEHDkpmj6dN23PCSGa7MIZcDQfQSKY6GOyv89pJEgZciRd0sTIdfv7LDnrPdHdCS5upCiTlBYku4Goi3Au+7r3I1wHH+v+aNCibv+JKRpbxEJEpshQsM7HM/ZFPs8vEvVuaL8JhoqpSGAyvI1r1m3EI9tw4r+kREIU+CEc3cbHAjHEvYBOrRF7beH4Pj7fD4rmHNoW2bGkeXNm85TkbLgJoN0GoE3RZPZDNmYxO+KoN8+SfZ4n70AwORdH4UmmyVNtwxSObwCHN3ijN4nqN1skkflTCWn0A+StGaVj96e3PghP55NS13n4ji1WWiJfVLSwiE2Rs9aWoC2CouViV79vmkyiC3q4JVzzht7JGih4r14tdh9hQPNKxDJkJWgs8bPAQ+R3avM8rnueRF8j2pikhTDhNm8emWq3Q0kn+D6WuTA2P/cSJ+ZQ53aOGNiN9404knIfayuMt3v6JChZRnY70KNPhncWWtFln46bSa/Kx3T3AQ3EaOumzbpcBAB5U2Tj0TqmKJiw9YVKX/AWDBUtgc0ittK00U22uQlJjmeLfYrvg9lYPvVIxMf5KESSHtYH+QVibdICT5Jx3HDMf3yyDZ0oyYKT5/KGyQU2bJEkKtqRJsUFpax7J7E4feIFamLMkRMkgmj8HVKKrLfeD0OOelX8nkMIn4bv2BSUi5ghiGeoJesYos08AyDMdVFR0MCdxC1cOpjEjyRt5PNtVqxw8axYJPkiL0NHPQn2tC3AGQG/Q1IeLDp/ciwdDjtwSrZ5Aq2UhWP93SaF3SltLUh/ebIwSWEJyQU2SVLAkbRKE6dm3DJJC1SczamcDQLbmInDuwjzax77Y8gxWIfokNa9y3+3ro8o0hCtWufimZxqk1qkW29WiHjSXzAEhyRRyRsoWsLlxpAif57xAhHIJpGJNqoCHz6RosRqssSCFAnDG9cquejEBQL05wv05R/4bTb/wzxwfuqQbh/Z5iUKSJKkUJGm6hMk03eTSMe72CJxXBv/aJfgR7zXB4Jmr0rHqpjZpfvw/67fzkVHhZGFqISsNcczN0myTtkirA9IiIoEROv5jKSNNAiwfAVM/C+zp0DaEyLw1iA+u2cfxBzNd8eJ2vOUJhmGJs+XR49Ey+VtbKC4UjnJQTR/AaLh34glf+d89eX5hFXM0bwQJDlbmyO4xaZ7or9XID6mJlkKOUKQRn84TKXhxlvvp5kQuez8tfqHdP/vesNEBNESkiJDwlqiiCFyFPlqkRaioiIJEQwkbaDJtnU8IFlC9Ua69xFtQaLUzHuhBfMdQ5MmgwBNmjzfHR2Ri6UwWFllWjCfCk7z4d18947P6pokABLNFlcfR/myKE1KCJLP/MOZyBnh8VpZhyO7ewW6G7lN9beECzGKN8IZMYLBIhnT9su8dSRGR3dRaWDngVs3MkVFS3Q8072PLaEWCVsETRPVwfXbRUCJNlCORjshcZ7EjZRA6duFM61DZIdQhibNzyFBgiCizCVwPjBCYMLIxVS5HsqCiJ/0FyaRiglNmixA0noUHxvB5y6lHbe8M6C4L6BQJ19kikAa+eGwTiqeddnHM4uyxOj7XYKY9p+NXouWBK5IpPlb0lpUJEeSqNJU7CveOEyyfsMGOfBvq9aEEd+wyEK0CF21PyVikq8ETZrM527rTf9ueGlKBlBKjqhOsIAZYJKcFCUZSq/5pEsphxR0RqLN/pHjnkrnbKsTkt1d3KZKlhxKn6hZE8zZxt3RvbsONt8/EFKEYiTtdaeBH6xZib1YQGKJi2faW29JaBFVi6y7XugcUTgauVlPtiF+0IArFvdMYyXylgiTMvJDDgrckrKQN5wscAdyu61KoShRcKPj/vnA9gXuYYkIxIg/W6RHURalWFImSYMUbz8KQZoZgjQz6yZuIt5N1B0UcaPS8R8OK1B+5QBJ0UG2DdJFru7d/rPa/GSXeluE2wlLXNnaUbQDKMKiozTXMAZgWMBiJdUqyRNtssMk6wkiV/FeRm1cHksby8baVr8X+CAmD4OE7Y92WFIt2Axp/hUMaKxK8BG7pDirxwO4AZn5C1RoWxRSA/dUwoHLpDAlHFKkR+JwrWy/neYovrPpRNVEIY7G3dG1s/rh7u/mh1+VkSIcPelindF2rFz7iKalyAW0HTv8tL/I813VkSGSfmrDZjXYtspm+ctwWLuYipzukAioWI/1HgalCAkqWrpy5Tq7zL/SqlUGH/MBO0k8TUuKFkyaPMXWpenmtkGYmy+6G5N8lJuPd9pyNtsLDxNDg3L0EHfp8sFNkOTPfp8upiLjw7U8SrFHojK3vwtlDNVE3de1JE2YOqxWd/rZ4vP+rzjyqnHUMfzPgZfOXd9fIYhYYm+P56QIKdq8eStAtGNHMBuJtsiKEIQZCZGfJdm0SY1qLy9ZVhK4orFjg9o3OiLcSGJvdbQcCVqH45KrYF7Sngm4bAUcT7oUHstBlxbCnn6LEkS3bJAES0CRn1xSMsQaxAwtIElaFOiStNo6rmVzpG5omrh74FS+cyndznRqVBeQvbhPxt8xvDP9UPHne8xRB8YVgRgd7RGyq/PAdct5ZJE5KlEBbYOlaAdStDWiiDtvagqANGwTDZOsDwb+efsRa5HfdMLtXDw3ZNLCJZYg48TWQQvOfABCwNDSFW7sdhnsJyGQihZOwsNGiaPQefvKJD1T4u+G3xRHHhrxKnYUSJbScU1HNpW25axvh8I0M7i7cjiWO3Vg5+7eqdgZxwMmRXZPUTdFrBGj5U6K/gVndaC3Wb7SBbQMigiihdh906MkYgxgE9e7V7C3LmaG8A7EvtjkZm7tz3dsUcnKddyAsze8WW17uTi4bTlaBsNucOJ2CaO0FFAqIkmaMm8ynuE1T6jR5IAii6zI0jxHvG0pYkfEtwXKLHlFgrt1RSB5s51xxPbcmKCZfB9luk2uDne+WPkJuqLuTh+67a/9Q8l18NFOhxCm4StF/+NfIs2nmZCtZIssRlvFhC1/y0IHURzPrBR5inDgv5h2H6EQqf7JJL8td2HJ8jUCoTWr1+DtbXEnUqxFdC+SYoPS8pUU2yh1UwOwkyer3hubeKpATmIhWrSAo1eoQfGSRlyVkARHs0NFiqJbjvatv9GyTOZ0wfLD4d29FNkf7H4dt06IURfFbPdu7y8tlqWiJEVCiwKKxob9W3ZTm9VIkqfIWWuqWKp9lNjNXbS4ZJUdSqLTkpwUraZ7ba8EhFYsc9va7D4AAZIVJQAJT26bR/uZEoUkN26yICgYLRJqlBuhMHXLaLGFgjSNji7lU5HdnCTenXJm9prqA51bMx/o2r2L351h1gGVIilG9A/s+kGxbn8ARSv9fBpBZDCSE7aqfRvFMw/RJj/YtqKEN0LSLraisaEcjYV7ka6Cu/9ttJfTojXsi2BbpL2W821IDECOIWjzms/Ibc+bTgfaTokKACxG1GlVVWxQorGJrd00jxnv+0547aA14jma7TmSxaQZuQUpgRJXBia+N7yLXgdWiuwBItJd23fDli52LOSmaGsmRUt0kg/G2vx2cXQknQ6xZInL7iJfBJXLJctposQgtNEFNStE5gOjGeiQj2dL7JwJQeRRMtkgggSn/82D82+CuMb+iFN+VSKSMY3AWUhjJovEvt00SXGLbd70tCLN0IZbo1QKSw6lDx+QQmR/tvvnmJDSxcj/E7t9sDDWIgMRVq63bvUUbcmiKIhnXKrcutkPkziKnBRBkQBiGrK0EMb9V6yhM2420oCbj2ikRMvQFJWgEhWLYAYnRyxZbO9KAmupyP/pHKVEtoZiNH+SaqkFwWwhIYSvfJJJzFIM0vyAo9mxJMlRklCSKMzlpOn/Hu3a/cuVIrh/o16dhy1dFPXzVzJFm0unCN3UcnG0CE7YbpG2iM7v8+MDkOS7BoqNb4uLl9ljkoCd9fYOgE6MVsOeyJQSiWDGazGfqGWIQpCMfZuPt1NSGLlJSdwN4MVoUSKe0QFKcv8ufSiYvNdWgW0y3m1AcgSn4Cbdtrr3H916m2+RlOBp4ofDOgVatN8HZZNipP6R3bu8vyjSoijR/wyMkaVoJVG0JFOKNstZkg0yoBVTRFvIk0lQ+4YDQMyfsYIh2rB+PerRRu+uV2FAW7ZCZGfFJcukFLkj2YoWM0jmH1iCijSW744TWaP5rk+rlWiBVCKihp4X2wd80BczLJIESY5FyvPZXE1SeaQZgSI55y0uCmh3dPvSpci29rvpf2ingR8tSkQ0ik2Yon22w1G0fHmJmkiSed3aUigSUgQEWS2ikqXdQ4vjJOs34K3b1juKVqMSYdka87OlnOFLJSoiJeIjIhkljm0L5tOhSuEckuqBYB1I+yFkZbF9A0csWZrg3qaLeRfUwgRH8xMpm7PawUl/SpLmzghu1iZuUckA0cvU94bV0lLU7QA18/WqFInRsEVF/7LVIkJCRLStxhdhPPsspmiJoih0RW6WxM37Q9Xa0bcQS9fwvrikBCEyCAFFWPJ2tgiEaBUwtFz6arLVS3hHSRE9ihbze4TJatJS1qT5oTWCJE100sY6W8SGmqlxZ+MsxhvkLrZgLXZBTpxS4fts5I/mO47m6NNHpuWqbgczbv42lYzWzKkPdAml6EA18/X6wTGBMxroDHacowFFn5kVUGQZQpYypYgmtdd5KYLzkLGHhhwhQ3To38a162FtsiStx/NsQYrcORHL3fbsErBE0g4ZaMQJo0WLxTJY4X228VRb2yehsRLHkY5n0lajl6YNT/CCAC2mT+ko08VskmKHFOrRFHmKjUNpWpqj4CbJKsrZEPfowMAV7c9TQnKtCscmxEhgJKuOhghUoliLllDHxHXeVgVStCUV0HxI8wy5OW2LkF02pq11iT7Es1UM0XIuEy0rFhn+YvRERU6O7A8Xz/dzh/2BJLkDtuFs3EW4N2SBlCLtqB1DfOIk/Zn0x/MxlMjSosXKI0VFbRYkfbDWnNnhLZE0Sc4opW7iPiyUov28qboMYjT8A9/IsDcWdpn+VnLXsRYtiSFa4w25jGgiQysuCqqV/pCkjXgDyU1Mkc3TNnI4owNrlvuCdbE74xgRKpIHHfujIQVGLrrZ8aaltADqxeInv9Dzw3/AIqaEDuB2rwvdB2lRdOQACtL8LIc0T4GkFWlGOEoyg+7eNVdNA3w0vPNXI0XcB5GNl4FLFnotWioi2lZHkcFIU4RzJFHPRA9Hqi3Z9hs0SPab2VebMLZ502bD0GbQoo3CFUEfdrndQcJ1Ipi7FTJU7O2QPxrSH4oUsEReqRhO/C+GO3DnsRYGr+EvLKSdvoJK0fePh0dAkrgcOWe6ryMl0jZnk2bM8DdZnjt3WiRFB7j3IVflQIy6d3tfN8RWghj5wRCAaMeWUItIipJpPpQd5ZZsLDnqCkGxbOGu38RzAHR77TV4Yo2MZyXkipYtC/L74kiHBEuL2dCEKEFCtwQ5lNldmddCGd5cKSlo14peLXA0BS1SlP9Pm5YsJLnsje8oOHNGlOUfuOG0hBgdFWT7XYZ/NJZ7IJ6i7U6LKKRpini+Vk618YTtDjljG7Q/ltCj2J1uo+YAUIrcHUdWu4C2As+JwFOOi4tZjIrsT9/LUAiRJsr+rItCBERlAFDKwZM4mDv+czxJnLRFOZs2SL6u7e88gYcuTZs2Lctuu+e5c10DrRs9un0JBUe/Kh3r/tGkRsOWshiVLF1KFPlU/zMIaVs2+6qjE6PIFuGcNs7Y2kTfje3b7+CAVgyqt8w339Z7iDav3+CliGrW9i6RKziWYVeXzspGU42GGne4FelD191L0ihJjly1qVheTqV8UTynKFEZYLHgaEFstKVBsvccnMcH5IQdkhkhSI6jme89EChRlwOxkTF71fiB+wczSB+4kLaU7fV2NEY72Bht2QpTakqLDEO+TBmMtWmK2JEX4Z2r/IA31on8NAmaa07QIJ7BOdmwrVYoETqaYmZoscuVglbFokiR2CznIMlDpGgSCaGucGZpUlaPbbLeWQte291qOfTaM/QoCeP0Ufgz7HJANjKWIkbKGg38aIGy1+vWrduu7DUmaQFFJXpMm6b9ASJsvFG5qMRtEiimUlNxiRwm2bRZUISuaCPFMzhnHQ/yQy0qptltLFZzhVHfPGTRovDkfoLJJ1+kSkURSEt8fMMP159zPOkvpeRpoTRJGqT52iC5IhLcSlefhxyOkUiQZr43TGz7gMDy5UqRWZW76ZDWpfv77uiiNEWYpOHxstIVJWzRFt++xRlbSvTBX/NgLhvrNX6YZAtiJFwRBjQMZyUKoiWWIfNBJaLwzg/+ZzfWYUTnaEuXtHBxVApwkuSAcVrks0L57GCLYfLlyJwZm5SkOXR7JQRpmur+z3BFAAuTi2f8Q+z2JVprXBW+F8hh5+EfLJBJmqXI22soO+7YGmsRQCQocsORu1C7eFLbVweKpSlfI87ZghtI2ltIrt/A9z4CY02eCDaQLMOhWxKjIqCIbh+iAhkDNBZPqx27aKwQpUVKkVRhkjkqdh9OexxSwZIZXoASBbZFixcmeiOsR+6gP9AjPrZrDt6eC25HIbJ/r0szPxreKYgn+3zD87Kvg48O8rTOAz9aSCUcK0aOIhHSHEUlwhYRREjRVhfRcO+R2JJNHX1LUrHz5IKizVyp3MRShIcb2zoRpmcIUnEJnhZBEOFx2fanxa6a54DC6Q6LEv80VWxb7FEqCpI2aY7iJURJgRSFtoWxQ5ofCdI8D9J0d1vcoPXPDE2b8d6wTkIFbFj5Mnr50bIVbAlS967D2BdZigxG21XdkbSINvZLV7TS26KtPBy5i4YABEW+xpRO7BgiGpDEXdkYzlZQoWgZhLMS38G3TTmLUZGz1QIZkiA7y81v4UG3iNAFgMBuF0u3zTrk6+VLQqCWLC7OqjktgoeMa1mlSCZpOimSGkeapppt0x4Qs2nwY/zyqtZyVTjWUewbId4YSTGCiIYFI5+1B1rE29eCsTa/sb+E7wlbrNIzHva3DNk7btmiI0+n4TEjULIuoRwfg5mNi3S+KKzFOCJQhBghOosW0PH87pg/6rQKjQozt6gWxEm+naJMylHGSvltB/mCxFSkm7B1w5FTSJBEz9ZL0mxsfYjV7UtroOl1yNFBXO02cCmm+iV297KFaLvUIkj115EvKkEl8gFtDXVMuEy5y2nRckFRYMnXMESOIdsEcRshV1F+thwD2jIUoiXOFNGAAJ4PaX9McFbxYr4ldlERtlvtcD6efmynPsZKw01xbbGvSS5UclSsxUivZRn8FMeRjf7sOGObL6Zr2SFNdlUkl/r7ujY0bqfNGdhN1hvNOvoAnJyWz6pxlA5pXbp0HbZkITa9Qy1CMdqydbulaImDwmG0LqFFPO+PFOk4SE0T5a0BJApodJgoBDQ4JGKZc9ZIUVExmSKECCJUUclyZh+Webdu3Urz77sQflKTFhBM4SysI2lxorjtOVqSYMgNfieJCiSJOruR0/a9ETEYOYVMkrsXjmiQzJjzQNdAAbocwC3VudfBR+uQZvcVWTFaujSw125mduuGdb4DorUoKBd9xhQ5WyS/S7kiskV4ty2jRsIVWWe9vGQ5CRGlZyWQ5wNHi929tYpLVtp/XfNvu9WQTAdNMEzr1i0vsXcutoNEcPwozjOO5TKSskg+ovmkLRKjZfy8TL0kVSkMa4sXZWds+iRk22ibPmfedJH8zwI9+khD1O3LGZPNEKNwRMTWHhcJLVqLPxeHxQ5HUQmHNKJIFh39QJLQopKQItU/w3C2BW4rsdkHNHsQpJMigIi0yHME/Y+S5evWAkBb8YwJPMTNTk16lAxJMOnI4Y3dNpVzIntUBPVH2Q5RDC3zOrSMPxHSlMshya5/cmvt5MlSkbiINJ1b/7NmfDSws+eHrPVXJUWJDSF2R8hi44uWojFaqypGSJHoo0kpcjmakyIxkUT1ohIN0SoVz7B/K6Zs8fC15cupTEQMkS1ChiDXL1m+ChACfmA6aRPOTG5Yv3H9xo10720kafmSorG4idEmbtIeLRIUBWEtqBItkyqEs98wYUDPifAmiwdkkhI9tvkLorNHuGE7xaf+s+fMsUk+J0Yunn011hpXxaO76dW96/uLIdeX9nrrFq8uSFFJjFGgRZ+xvXZaRN9UUhK7oi1b3ByAP1V7NZyPDQGNGrCGIMjQXMHIQFViEaLOiZ1O2mCUiChavxYxwkUobV9pFAn2no1dwAUBXdI2P2RhjKS9FiDRHqZlhNCyYvHslSqlSVxWSLRGoq6/myCB9J/80expD3TpDj+sLu7HdvSB3saYc9X4QVf7L+OvbibdXxyn+qwvJklbqXzRci4Xcena/V49HVlChrwk6Jms9RDxUVubNogMjc9fQ2ddwhwBSuZasYoPBdjEGNnlOCKAmCO4wdJ2A9Iil/qzO1IhbTGSVFTMbRAP0TIfzpggfHUb5PzmlGUCoaCUhAXtlCBFpyG5uDYP71w6zST53dxPDZ66foXxzK4K3+uqxchYo6VLllJEY4q2+l3W21ctBVkpSWoRUBTM+6+kHA2DWomjaM06laDt2LFrCx+15e62bucFWIh8TLNPhqsVMA1ARyVt22YYwlnJ9RsIofWCIRgQ2GiPjzAk2dC2CIetZd6/kOfxGSLsx1pFWqKVSAAT8CNAUnsuxYSJRikxg5QQJJGxTR7WGf5/tx/w6NL1K2h96GVjmhKj7l2H2f1bKymkiR4IUrROaNFyBxHt/5ARDQaSVETD5+XLEwma/Z6d7ma2whaxEiFCIEdwLVuxYrWFyOrPNrM2mWub0yKP0VpCiA+OgJNI1toawNKisTZniyqQYcUo4IdjVwBRehW7ACfyNlVDEj02EdbkoLa4V4QtIc16oGt3DmddEKOvNp7BOvKYUIyGv7+kRIuRLwHZJE219Je7fUfu6DUx1raVI9ryJahgJTrNl1MAu/zhkXyfbEvRCueKSI9gWYY2brRbRqyWbTNSZJ82oTWS4WwtQLRRnGKzhm5Qsn3dypLihbj/zPlr2u0B4yahIVpWLKxQ2VZW2gbNkUSTTRkk4bXnTZn+0fDuXZghfNq/N2Tcu1XjqGOkGpn3XYZ/UFKiQ5rDyFK0NIBoVUZEw/YtULScpajESVEwSmIY2rXLUSSkSDJUjBAZHYLN2BYiC9C2LQiRUaXN7IrAWbO3Vlq0Gi5DkhWklSb9X4Tbh7iM7RuzEURJhmi3rnhRJ3R5J+5jm4prgNGiVOofn6g9Zd5Hw7twJCOODuQRjvmvit9TUmQxGvjBsqVM0XZljLZulRTZXslyntJOUbRFRzS21nGGtmML9m8FRatkflYilGiFFSJWom2wtqAabXPxbO16n52t2agYQohWc2izIBWhGCwUpgj2A0QtD2eoFUP4Cm9WLFsmaArlaFkqsHENaVEi9Q+LSFP+Nax7F8mQ+Tjgx8zktw5W1sg+dX9/ZYnXIilGW7duX64aYmGmLynixhtFNA8RBDQ97b/TULRrpzsOmXZCEkTLEB9myG5fsxtpN9lYtgWkyPZOKKBxOFvrGJIyRA/6cJK0dEnxYle8XriYUzMoSnlHXZwtQngOBZ9G4bVJkxRwVCxs9iKHkSppi0EkA9GkBzpzHGOO9vf9GPd6/UBrkX16vwRT/TCkGXu9UmiR34/NVceAos0bfO1ahLTQXIsxAEcRTKdRxVEaIrh9H4UzwAflaNvmzVKKzG9wHAmMVuPhfsSRfdi72oIm2Q7hsmKedcRGvihVK4Z86MJzTOhJvDJXniVXWcqoa9tYumhRwiCJU2w+8P+rM0pHftX08Kp4XDfpi+zH8A+WKYq2Qj3HatEGSZEtLEUU7fAUwYztcmGMlquAJrRol4xotClbwYcMEYCgRFaKLEU7t6AabRN5/saNKYIcRWrRDZLXrcVztZcvX+6jkNvOHWrQCibIYcPvlymkiKLQKenCtrPaUpEWRIePTDHO2vuOr1U8s+vgo5UW2Zg28IMVzhgJMdqiKSKMqAWyQY1GAkZb3USSNNeq5kiJ/i4aspUUBRitKLH3EaUt/TY1o94bqBEr0SaEaH2E0eokQookgxLdD3CVO//PUbHM4SH0JmstExLFtKnakq4iSUVKbxwxKKEpUlr01cw3Zq0jXTHUxbRhK5c7ijxGW4y9lhQtDWcdA4poro1xWM77jpwd3yxGbPkI0rVsi9gVOYwwnK3dCJ7IOuudW3YyR5utFkEPzWdn0hDlYMixhGFOEGed/IoVq1fkWCuD1yRTcD53QFI6sgWnIYl0bcGwbt27aS3q+hUNFaVXjeNkjobFx/eDLG0L7g4y9npJbIzsEefbUxRtxdGQ5UqL0hTtkhShEq3wQmR3QPItjdciREaIcNmAhlpE1ca1UTijg9fxmFHggy77yHvhN3iqVmt+Vq3IRVJcAsi0SEqPeJ7t/a6duwW+6GuR5PsF6b7QIsKInNFW54y2Wnu9xJ0XYo9LIIpoej+gaMfWtatESFuuJknkhuxdOz7btYunbCNbZOTIjjxCONu4FtOzzR6inWivkSI4yU+lZ6u1FqG6EAwIxt6ulfjn4TskUvzZWSypUlKW1Y5OZ/uga/du3bQWHYDbC+/bqnSs0qIuUHy0+4lcSCOMyBjRiSFIEeRza9eq1q0zRqtENwwhsj5KH7zmO290BKnM8923UnZGzhoiGUQ08wZsEab5QYa/mo6o9fGMf9qsJfSTT8Q3/X35iZUTuRgkb75LB2kR5f+OIyw3Si3q9qVvYix9HXmMMG7I0/APVkl/vdVGtK1sjIrYGPEAAIc0gOIzb4yoeG2LRrJy7SIafsOuzz7b9dkupGhN7K2XOymypmg9BMKdO5UW4aZaSM9UQKMi42oKZkoxOC4RR2lVkjrmSwS51iomyvMaOu+snE2BtNBhtOCjgfpnYx5fh85HuGocJXM0/Pcd+MFqoIhjmgXJVq8ppNFpD+7ULJqMTFMkItpKFdGYol0Y0jxFJcqVU3rG8cw4IYvPrl1Wi3ZSimbPidjoO7AinPlotnoF64VyzqtXcKBTLAlc1nDJco1/odplaU4qViZPUnE2RwudHi1YOqyL+tnYt18zU4TLxjThi+Dfdti61YKiHeYyyxoj2HGEIa0kleo7inZsxoIRpmei7ya1aAdpkRWjhBbZ717hk/wNUCkCV7TLXDuxXrQJ27Br1ytfjTK0igXCARQ7F2eSErLklYiQwSyOGXL23aMV05QASSiSPrhbggQcFb3fhWvWbn0N45ldlY8NBh+tw16FFDkxMlq0wYY0Oh2US0YkRpG9/szaa+FxXERbszbhizRFJRQFIZ4txyQfE7RNaK0NQbt2WYx2YkDb5MqNuvW6KtAGIkYT5Mni1C0EyUUzL0eEKb/z0XPN6lRxAWQpCnCpRlux6tcuel84a4boS9+Uny9Gx4T/qp2Hv79GiBFq0QYb0ugkK2uMYKZtlW+6aYq2cNlxOVUcfYomczSYsaWRJB/RWIkYPTxfhKVo1y6CaAs09Df5/pnu3zshsteq3BUgF+oyLBJbbkbGfUhRWiPiXxzgtBrJU+CTemSk6AOYblTryzw0rWyrxve7Rmo0/H3pjHBhru+SfT8ZySFNJmk7Nggkli4PMn1/3tEur0VrdY6G+K0gAVu/YRPtPAKIdliOMKCtT0mRCjArVsU6FCqSd0rOHgsYkI81HiRCSK9AneIYlxHaVFxb7AQpAVHXL+Vo9L3EKIpptqPmK4+kRttdSCtawrvXVrIx2qwp+mwH2+ulkPBbiPBM9qAZ+5kLaT7TZw2z7bPV/mgITPMNRVt2obu2tSLQIioVUZbPpmiVCmarFTJJmNiErw6ctgtSYkxpjQJpdczTmliTZOK2LPRIelwbIOoS/Vi+9/WFSDXU3DIYCWu01VHEWlTiKdqeNEbrvBb5kOaOWKejanZRRCOKvLn2SiSkaDNLEYa0Ldu2CVekJkG0EDj3nAMhV0JKFSSdoU4o0JpgMFcxxTjpf5kwuIkZEkfSyuHdIoq+Vu2zeFVOYDTwgzWaog04HUK+aAlStNIXKIO641oKactFK9aJEW9g2+V81GY6OGs5GiL8vlXc+9jAZesdjqJdMHdNUhRWimShaAXXrXMuHdAikNa4sBZqj53MXQ3juSyGGzVDaaYVSj62kSQtXjow/okcyHsL749V48gkRquVMwKK3HHXFNFWZVG0ZQObnOWudI3HRro+GlG0myf+V3nuUImEt4Zv2Sa0CHyRLlvLPr4vAa7Ae4aWApGz2Ku1GK1RJUcmdONqh5C/1niUaOY7nNfN8Eiy14YcLV45LAHRkV/neAYY/SBK1LqEGG2AkMYclXhjJMdIPvPGKNSi5d4XuR1s/Lt37XAUEUNurA2LA9xB26Uimgxoun+mSOKyUE6UXD0yI01bLWZu13hUGJ2N+itxqJMgrcjmyJYjFy8d1iUKZ1/lbup8V4WjusYY/We7Cmob1q0UY7MxRTpLc8ZIhDQx1iamI0GMmCJRpxRaxN38LY6iHTFFQovWrBblohWrYk+0PAWRL2OviBji+qIIZ5YdAmgjfeKHvjfSr2rPFNcAEklb8VI/DOLTs690N3XeGB0X/nt36971P9t9V9aewuFG1ZZyRAsmI2VI2w5MLCWIVqk57Q1bt+gACCNJqEVOv1Z5iFiLdgljtAUyfaoVyVl9kRup3lmQISUjGhewVwQISR2SkSyPtYa2o2xMsxRaJLMS1cZuX+I9GvZlVTw6CyMT1LZDkrZdjqotdWeM+GH/pDHiqqOYanMp3a7PvDFaK/povlKpDhLdaQtF2HszIY2GQsJxfV24RlsUZEalsJRo0cr8bHWoPPmtNaIUERS0Vru4Zh4rUxB9DbYw5rVqxDXsbl26vs9qhKdvKDGi2ZBwvtZXjKRVBor8LsjgNwuK/CIpsp3YDT7P/8y7a2x/xPFMiVHKEZWYyz5CkGgWbXVMkNAitEBiradrI87sutFdeepEYnfTmpgj+2+0/P2ucaHomMpff1NEGB0ZY9TNYGQ33Ww3aoQhbanQouUrVwbGaEvCGHko1sW1a5Ci3dS/Vfaasro17jbr1hVRPPsMQ9o21iL4yfKNr0P3scp38pmh0nO1sMmvUvtAhpAYRAafeHyXkEroUsSR+7d7v2v8Uzjma5+eCYx+kFajjZYSwxFt4VqylHyRNkZB4dE4nbUrRXxatXxVthbtdsMkCS2Ccf9NLkX7bJf53WCvt2121aI1qlgka0WceYVStMzCtCzGh4vYSopkZiYQWu+nvdc7HaKvrddvN67XyiTCmxakpBJ9A9IzgdH3Y4y6E0YWJKBoqbfXSz1FUox4i+yGVVxDpIjmTodQWgS+CENaTNEqSRFmaLtZi3BCLbBFQTt/RVzlK5GmaFmkQytWJOw1ahyaZKk/GwUl0dqo3rJCqd27AUgrVr8/PPZEx3yNu2epVSGF0XAKakyRkyI0RrpiJPZZQ0hzpR/XjN3OEPmI5kLacpekuY6JPI92J3rr3RYkT9HasFYkEUqOFcHmjJIVYndiKlULbHUykG2UyrNR8OI/W68VauNGFeOCrO39uANrIPpmpGcCo6PSasRrHYjRUghqhqKl0hiJZB/HqbduXxlGNHeCn95Nyz0TKUar5EASY2SM0W6UI+uLnLneyGeDsBKJTmyoReCsgSFkKQOkIKStYWMt7bRHJCVFCV1yMXB9vHPO/oOSEH0jCkUBRsd1zY0RiBFpkbPXYeHRHeSXiGjbuXQtq44pY2QxWrtWnQ4BztogtHs3UkRapDaghSNiqaF6sclVI7RaV42EFsm8zDG0sXR80kTJk02cjK5LpfhdvyGFogCj78UYWYu9jilCjJYmjJE4ZsSFNBv1ltpnXXXUWrTbzWqvStmi9Z6iHeCJPkMtsvYaKQp7Hzyw711rPCS2Agz2MtalTC1ySiQimQhceehQ2izJ9M2StHpdylh/9Qem7SVG0bSRwaorNkMsMEQRQGQYCgqPW3eIZUPa8qVCi9gXic3bLkn7bIeuGK2iu6zLs7FRi8xvRy2CJC3QIpc9y6AWTqxSvypFENeXhBahsVZ5vXY8mWtT8KpY2igUac1GC1Gc4n9VN2jY91XpWMONvECN/rN9tTNGSzmiJUKaj2k7oHy91HEE7nrd2g1rpRZJMdLGaJVP0dbzXlq7CXK3ZQjECClaG5jUNavlTlgHkt1HQtmhraCuXbNqBZ06pMNaUHf01lqEMyVFKXY22b1zm/j9pgRC3orjWr39P1aJugbX13RWP5918LGRFlmM7P4iq0UCI2OvSYtCjNzhWcuXEkQrxfnYMF+kIlpkjFYFR4xAuQgC2m67bGCDsmOw+WM13P5a9mItBhvXbtAHA8DRAxvWrsbTHEIxEuFQ+2omKIUQshPLThZLxNB6CVGkRF/vsbTcq/LRoRZ17da927AP1jiKWItWLhVipGpGaK+3r1pOWrTSYaRyNBHRTEgTxgiEaFVAESb6uy1IlKRR6dqXreFjlWDA7qrdxOfcwLy2/WP4KJS1a1Yrgx30P+AP3Sg9UVKGNrnnTZv8K9CzKf59oUkyf+7W/9gufqhEX/extNwrGFpDjuy8EVIEYkQgraQz/KJjRtx5kLRW8s1ntiNFW7aqgLYbQxphtIriGaRoG0SiD75oN0JEqf7atcEWojV+iMP+9PHYrG0744XHoSBIMkUTDK2WvjoZxFBkGKNNqDpwFs4m/4nUok0hSObaOrBbQom+/mNppWMktQieAaOVRJEyRlwzcmq01YUN66+JItx3BLOOuuyoQprBaJW3RWrn0ZYdnqLduz+LKFrNm3lcw2Lj+vUSol30BK877dfwHhKbPUii4Og2daApotRsY8AQ6o6ExnGkPsXfRcgFJG0d2K1bQom+4RAFndmuLlOzO0NYi8gXuZAms313kN/W7XB8umGIzurHWf9UI808YMbIqtEqP+rvtn9wuchShAUjsNfQSHNKtEY2vfD8PgeRw8e9boMLb0SyfuNqVbReszrI8ONQRgKEBDlqNm3yX/HP8tdCjD4YnsrOvkkd2KxVQahRN3ZH3boNf3/VKi1GeNJ6WDTauoNOz9qwzonRKr9jJOi57XbGCM92XMURzZ/KTjmac9cQ1KhgZJyPx0j+7BkiDmG7du3ZucezRCjZYyLtMaObN63duCZmaI10RJIhClwECZOj12b8PRuYJSFPbv13eEKIjBJ9U3N8uYQ3Yi2yUW34+ytXCDGyOdpSGdPcpmyLiN3Gtt2WlMBbI2preRNkQot2kBgxQiKgidL17t2fkTOynbT1IklTOuSj2Tbc2m/WHrN2GpAcR1aL7BOSpDZJ6rZZhNAmH8g2SeURn0RcRd9m/6j3E/vOvvmeSGIUalFX2DW7QlDEWuTyNDftv8VABPd+sI4IXBH4It7Xv1mWHXfLwqPkCCiy5aItlGVhRPNatFml+lqIhCXaY3Vo105L0R7D0s49u0I5wlPYxUmR6HzRDsUpmRSiDQlYYrBUeHOGe+t/oIn/7fNEHqNjIy3q1rV712EfrFiuKEJrFKjRDnsmugltG0iM7LPckBa0Y3f7Y0VDKVIt/c/QXX+GJaOQImFktK/euQd0yPJjg9oeHde2MUe8iKRNieKPMEORGuW3nGmyvvo/idZZt27HfmsgOsiWH7t2076IUrWlGWKkgpoVG9hSu5I58lqkQ9puuj6jLbLrRN16A92txvkiCmifOWO0NqBo4xqGSEgRU6RYEhhJkjbRQ/7c1/M5thnBqkwEuT8EM/xQiLp+o4uN8ap4bNdAi+Bp+AexFoEYbacStuMI9x9ZT2QDGrnr7Qlj5MQIbqIOtzvCwjXZIu+LnLv+jHogazVGGiIKZxzK3PNOwmhPkiO621GGrymFoW2b8l3gq+MG7LHfkEn9vBd1+KUW2afh7y+NOFoX5vtuF9u62BfJXgkPGO0mf23wQYxkL9Ydsm67seSLdpugBDcj2rjR7fva6D2Rj2d71Nq1R4Y3qUeJ2JbGKQdA2zxJ8Nm2TKwwOYuV6Bvaxc+J0XFdhRZ1dcL0/tLiKKY5Z7RdYbR9HUqR+fVVLuiFY9q7pRitXbdu1Zp16wKItuzYCb7oMyFGbjhkzRqxQwwhYor25Fg794QgxRxt2rZtW2nsMCnbPEr6q9EX12/+j9d5qUTfyHmi0lbFo7oKLXLmyHjsYlEzWqqtkR32D8SII5o978gmcmFM2+2G+C1G9HC2iNw15GiOot1u3pGPKIa1Ni1Fn9vH50mOpEHalvDbm7IlaZsgZ1vwVr4TTMG3WV/dpVu3SIy6HvVtC2e4eBZbdWe7dTceu7jYM7RUG2x5fBaMk6yyGK1bFRojVTICNdqy1cLDEClb5GrXn4UU0ey1U6IQol2EUYolsN468ccr4MjToO3PttRjkyRn2yb/xHj9FztnXQMx+ubNWOe7anz/6G6BFpmrS5fhKqqtzFSjDTAhSTGNtGh7zmTfLydFXC5SWrQ7GL525UERz5wSfU74fO6I0nokczaZuwUkbROis82zIRRH/jahRVKgtr4/vEsXbRWwYP0N2+1RJoxgn5rWIvPSpev7H3BQWykp4sYs6dH2DUgR++910hjpNG03nUGThMhqkSo7aorW4lHFa1GKtm3imrVjxUD0OX3EsW0nee0USWEJwIODpPjaAHPjeKKv2W8ScOloJpToG7XvrMwYHXlM10CKMKwN/GBJlPBzvo/7sl1IW7kuhVGQppEYCYw20Lgs9/SFu/4cMKKxWdWpEFJkS9YCIQKJIlvSa+/ZuVNXkrRL2uQSt0Bw/Bv6PVm/y0SzLpIg97/nt6H/mhOjykd301JE74e/X7w0S40gVQMMECMJkUNMTV+7A7E2eCUSWrSD3bXy125v49rAFAlrDeTstgh5lPZkSJKrJmXUJJ0CbfK2239FRC75mxxdxldzgh9q0dHfmL34e41RxeO6Jla3bsOHLU1yRNsgN9D+fi1GfhRpSxDUmCK7Ioqwj7ZbLhPjhBitdUrkx0GoNvQ5I4RaZBkSNildA5BN29huO4KCgLfNAaW/Qq9b/mMgSv1dHlfx2w6RWRW/f0zXqEBmzZGJasUxRrR9zeoRbqpNiJGc9pchbctWjxAy5G4PiifVKIzoLCy3McMeO+ukCNv4DqDP+d0e744+3xOmbbt8cBOatC1VkhTSJDRnmyRnm/JH/x1oCyfxX+Qx3/92ZvjhqvCDo1Nq1KXLQM7VVorqo1MjixFSZBkijNQo0pbAGzkxYoy2si2iWcfPdgf+mo53dEMb4KzDBO1z5a09SpKmpCLtSTptLTKZS/8GI0RdkkJ09A++rRl+tI48OvE/UddutsuvOFrne2rA0fbAGG2Xyb4LaeImIgKjzWFA01K023ZB8Eg1N/lMP7idnKF97iDyYrTnc5GxebcdkrSL7PaejLiW/4J/qR02N+ua+Es8+siv+mf7Ja7KsRohR9Tld2IkM34ECcRoHXG0HXZB+naaxgh3+SiIvLlOhLSdfOw1e6LNLEWc5gcI+bC25/M9sd3+PKFHmVa7DGuLzc3oryxQom/0Xo8yr4O/F/yfRH8pXboOWxl4I+mOcFftynUOI1dRSqkRTtpKirbgwoD2WeivQYzs4Xyb7WPzts3aFZnkbPfn0XJRbo96v+fzRHV7j5qx3SZq22XByQqR+19P/R1+79s1CFL6qmE9tlQiEuhuxmQvXSY7s0qOaHM27CBJFSa3+EMilBhtpmKRcNfOF33h94LshFs5bAYdAiVy0/qxFH0uA9weX0VihD7/3Hfd9vju/64IpG3ug9ZOftnpP/e/0dhqx49a37TTifbHqlD52K5SiVigu3UdPmxlcSxG66QYCbhUnraF99N+xluzt2zdDEIEd1LbyrYoaIF8IcRoM3K0TZiinTho/XlCiVTKpsDa7Xz4njBv00GNtWgnorJzp/sU/vFCq3aCEA0X9KiJtMrfGV8tV+Xv6b+Mrj7nd8maangAMOu2kxZ5h63S/S2eo892sBhttvsf7dY1p0U21d+djGmGI3hYA7KTrbXBaLcFZHeSIVc8Cm13Mq7JWe1tVNR2Hol1Zycx5IGCZYSoS7ekFn3vu2WJ/Kp01NFdxV+G+0vp0n34+x8sW+Y6/M4bgZvmeKZUaoOfi+QNtV6MNm/AKX+tRaIF8gWqER4fso38kB3CRyUCLdrlIfri86y1J3h2JQDH0c6wWcudkUCeaDOA4wy++F/O77sFWtT16KO+uac57PM68lipRT6+de828P2V1h6tDOSIpEhoka9LOmskz2IwYrTZXBDSDEVKi4JUH8Voi0MIxGALUoSuiCDa/UWEzp4YJPVGRTXXZRMc7dwWPrY5euixww6BuHRWadGxR37VP8mvdFX6njLZTpRgei2YxSaM1tGBI+skWnIuUgQ1FCPkyD47LcL5Iu2LUIwsRvDYKZVoz67d7Hi+yCFHssHm65FqlAQ5CvdEBsD4r3jG/utqRKEWHfO977AQwap4VKodAn9Dw99fKVM19tNOisTn27U3kudEbIHBf/tlW7vG8aIt7Iu8DH3hRmfJo+yU8Qy0aLcLaNkhLcN0cx1J6dGunan5ESFP8lPjqrt1FQxJLfqWTjWWZdU4MtkPgb+igR8ULxOz2BqjOH8Te0Y8RDtg/wgy5H1ROF+kYpr0KbtoJGSXpciHtN1f5MGO8t+iDrmLWrS7du7J5CgACdP7rikt+vbsW9yXVePg41L9EHgZPsy6bN+cXekg4pAmEzWvRpYju6GWTooAMdrM4c73Y3frmPY5Hq0GHOHTDrrP9R6bohFCX5Aa5SNICq3dnwfF7F2IUBZHHqj/+vQ+0qLjDi6HCFbFuD1LtSOb9FO2ttI31sgWkRap2rYManAWzI6tW1iLXJK2Y0soRl84b8S3td4JDFm9wHi229Ucv7Ba9MUXeQY2T1KiLQI5P0GUzdKO/2D7vqsP985XH/2D8mjmVuXj4t4sPluOVlo9cuaInvxDpWpejkh2trAxgpAmkn3K9T8LOEKM4LELgs6uPTTmSBABQ3blL0fedCcm2Xa6uZE9KZx2iBKRjGfw93Pcd7VIlF4Vjzo6pUX2o7PJ1lYukw3alSxFXpy2izmkrRsURWyMgCMR0rAf+9luHdN2uxs4IkTw2LN7l6s5ghR9jhSVRY32iGRNcOQBQnY9Q/Bu13/t/nuZ3gstOrrcVodLT4v4zprVo+Hvf7DSbVVb6cVIBTnf+fdqtHUH7M7e4iEijHZQF0RJkQhqeFtikCOT5BuMXERDgmxEK0NI0w47Gq6l45AYJbdne5cqM+o337EpkHyXGqUVWgTvIOv3JUi51kWZmlMji8+WLU6LEKQtnOzHjf0vaL81IoQLghk4I45ozFHZrJFP9j8PpIiPjNiFPf89O1mUPjOmOrLU7pPjyoUotewMZFKL4NlwJMNaCFKgRn5HrYBo81bfkXVqRFHtC1983OPVaA9zZL6Kaf4XnzNDX+wFS3vi7qxriyBRLIM79yQYEm++QzONZVw1Kh2VpUUwMmJtdpIhUdh2Fltu8N+61VUetwqM8K6fu1NqtMcLEST5rubI6ZmLamiO8uDI2aIEQ7xlxMFkGBrYlbtCsRZ1PapSeX6fuSr84NiuWatLl+GY9ic5WhkUjrZv3b41wdFm1wbBYTXw2LgdhNsgVot2O4g4zd/9+R5XcowXlpC+EN0RQuuLiKTUHradKrohQxkbPLBtVi5EuVflo47OwZEtHy1NBbZ163T9ETN+dW6NvTZjF4QcNjXUdoU17D3WYRt0KJjZt3tcQIvimYCIuyNfJHtt3mILiHbtiZfVoS7ZDB19VHl+X+qqfOwxmX+B3bpbjlYuozbt0oQ52q4qkBu8DG1ALZKJGs+r0blqlOx/vtuJEWJEWvS50KLdX4i6kcvYnCAppMKolrl9DXRo12f/zcnQMd/x/n2+K1dY62pP7oe8f2XAUdDiJ4+9YSuRhG82U+lxi/dGu9So0ec+pO3GWLabsvw9aS2SouTbI+IKvPceh5Eaf3RvgaEc//nlwSzvdfD3uuZc2O1fmQBJz2hv4MC2gYf4N6C/dhbbqRHoD4e0zzBNsyZ7N/Y+5HDRF5E1YrhUly0QJpnuZ25cc3lZ1vrOzefvy4Kh7JwcDYN2/1JtkdapTbNgsnFn9lYmaavniDDahfm+VSPkaBem+rtpS7WVps/xirRod4IkB5M32EqOsmqP+TD0HR2t3vtV6fu5OOoG7f6lxWiQdHdEpvx2bSVBIoagBLDZDYm4nbL+LkW7yBftxuE0DGtcuP48AZAvRH7+RcAZ5WlfBILE5UdJEjTuu+Vi6Pvf9WG0sq8aB+fI1qBNO3yYSfyLlwV6pM0RjR1ZlLZzTNtAzRCRqhFEn+G9ZHf5GxMDR9Yvfe5L17u/SJL0uZKlz0Wci0uTrpD9uZOhgcNzeWqbmZWPgOzNqnDkccfk+Gvt2oUStmXL2GtT6WhdYI+4frR9gwcJbPYWFdX8sok+JGi7QYuENYqrRTqqhVrkQNIjkj7fty+f/dem9rk8dddjjvtW3M3jK1k1vn9sTo66de9iAhtl/qlatu+IbMBTR8Al4bYidySWPS50y64tpEe7fJqPuf5ujGiuo58Sot0KGqFFn5MUhRj59v7uz2zLNacMmez+O7hhcT+uCj84OidHNMi2cmnotNdF/mjDBmaJkzVXf4TDi7Eh4tUI54rkCWpRSMtzURHgiyBZs9WD3Z+5EbRshsp7Zvu8Kv3g6K6lreHD3CERS4OsX/gjyvzZbJMebSUxsmq0ZZeObBTYrBjt8hPVeYITvYui2uef/3fg8FL/447+Qbmp3vdVis0mjga6lC2Ka5IjPJcN9WgzceT6againQmIdlFVEELa7i/yoiiC6vPQZO/53ESyPBgqN9X7aVU4+LhSOLK3FLG9WgPSsmQZUp1d4+LaVknRTvvYuZMMkgJp1y4yR7vzV6NMnHD91yZlXbuVxtBxB5cHs/22Kh58VCllSHsWW9eBw2zOVrxMHDfidx8BQPbWfL6qTYdAMEj2lp0wKBvGtV3uUEceD9k7kqiWvfu///lPKXk9rGOPOrh8Em2/rgqVjyrFZ1uQugNIHyxd5geR5K5aK0lr+fg1BGmzW16PtuwCScINjTspru3GCuTnweBsmddniFCX0hA65uijyivV+3/VyIMjW0TqYquRH9hd/hFIfISfJ0kcG7rZ3vN1C2JkQdpCo/u7dvlczTb33Y6isqZqJrn77L/GDHUrXYWAoXI/dGDWwd/PgyOeQzL4uNuri12169auQ4jWO4422TOLUI22WZJ24mPnFj7sAVly7Xc9JJI3Qrv/W3pWzwx9v7zregBXPnk/LZP+G4yWB4K0BmPb2rUGpLVrCaRNJl8jlFCQAKBdW3bykQxOkyhbEzsc82Xov6W1Wv0qz+0P9KpR6Qel+WzPkc3/Vy5btmKF8NoGozXbUY4ApQ3rkaNNPu/fRopELgnVaKdM+t0eR9sUyYeggXkk9bSO/UH5TPWBXzUqVT4un7iGnf/hYLeXr1iBKMEtsVxgQzUyevT/2zuX3MSBIAzLmF7bbbcfqxyILTnQXGKU20S5TJxABgJSxAzKZqqq29hkwqSBGWPg/1aIKAukT/Vot6umEo9kFp+LR3b0jLywv3Ixid9Nk1dlP4j015jEz8ikp/fJZPy8TMGhjhgonXmKRNU2H0nec53EKj1ySKpccuMdIbb5n5JIs5dZq2GzAYlj0qqpkFarVmJ733ow8v5JCKJ2jAzyqaatQplWaO07JBoWxrdCGo9Gt2Nr0sOEVaqqR6mPniZuG+ibTWyc23i6rBPJTsOSqOSCkotIdThaO5X+DEnUi4lB4xtfg6gaMgWOqTsniEuvjq1RaWTT2wO/MEImPT1ZjXiN8fOPZ3ZoShFJRszOZdZsPVZt2QwT+uVm0bQKpPZA/vV6I9DIXyDuynD141QMC8/E1pKJb0lSVKrLI9lXbVeoTRuRZvXE6/nMWsQiNfGo2VC0SW187f8nHwd9u/OtgdqprEBnfzqiwLfS/ogruydVVdUi2ecjzqMX59F81oy+XjYD1upa2+FKIO8+bNuhUgVIZSclCgbxV89qd5tELnG9RDq9VRa3X6/eBCIst/YBtWYLSfARfe4ONIgvMQ7gUA+IBnuU2m3GY2mfxCXi+yv5dL+gwkkmrU/byH4iuem/WEjhw/IQVh9ZlngAXFBfw0q8cyFUufE+jfxEJmZcRyfh9TPsn5w68j+H6cNkJlc4o+4bAxJpj6Ztl1DWqJqbW2rt3MfNl0eo40gTUghHQ/0kYpEOLJK6I2GFkMd6TbjXMVLXpHwwhDzWfyJq/7XppUip0dTUIwydB9T+h+rLO7Ydk2kVoqk/N4JQxT2JSYnRsQrxhONMIZNyfXTndgzUjekcBp0/ZFJpsu5VSpPMlDgTuiRCym5JknakErVinMUg0CUiKv13kVIIdNlEEbVvlOD2vVDiLVBGKYwaMXTzF08UBINwqApt/p1MaWZ0oYbUyqOXvyoiVknFuT6i9JYCWuexYoGgzxUjgSkucq3L0pgsS7gI32FVymVzkmXGlKXWeRFL+Dn1LwC9IrDhKS7YKZKqZK8Y+ajZmyK2gQfqAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJwLvwF7AEoq/qo8cAAAAABJRU5ErkJggg=="
          id="E"
          width={581}
          height={581}
        />
      </defs>
    </svg>
  );
}

export default Devices;
