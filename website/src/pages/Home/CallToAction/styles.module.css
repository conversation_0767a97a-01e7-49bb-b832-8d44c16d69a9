/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

.wrapper {
  text-align: center;
  overflow: hidden;
  position: relative;
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url("/img/homepage/cta-bg-pattern.png");
  background-size: cover;
  background-position: center center;
  opacity: 0.1;
}

.container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  margin: 8rem auto;
  max-width: 600px;
}

.title {
  font-size: 40px;
  line-height: 140%;
}

.primaryButton {
  background-color: var(--home-button-primary);
  color: var(--home-button-primary-text);
  border: none;
  padding: 10px 24px;
  border-radius: 99rem;
  font-weight: bold;
  font-size: 17px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.35rem;
}

.primaryButton:hover {
  background-color: var(--home-button-primary-hover);
  color: var(--home-button-primary-text);
}

@media (max-width: 600px) {
  .background {
    display: none;
  }
}
