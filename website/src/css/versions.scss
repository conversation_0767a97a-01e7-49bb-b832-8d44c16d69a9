/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

@import "shared";

.versions-page {
  max-width: 1400px !important;
  padding: 28px;

  h1 {
    font-size: 3rem;
  }

  h2 {
    font-size: 1.66rem;
    margin-top: 12px;
  }

  code {
    white-space: pre;
    border: 0;
  }

  p a,
  td a {
    @extend %link-style;

    code {
      background: none;
      white-space: nowrap;
    }
  }

  table th,
  table td {
    min-width: 100px;
    font-size: 15px;
    padding: 8px 20px;
  }

  .versions {
    margin-bottom: 32px;
  }
}

html[data-theme="dark"] .versions-page {
  p a,
  td a {
    @extend %link-style-dark;
  }
}
