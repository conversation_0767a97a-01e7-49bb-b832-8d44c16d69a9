/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

@import "shared";

.homepage {
  width: 100%;
  max-width: 100%;
}

/* ActionButton */

.ActionButton {
  padding: 0.75rem 1.5rem;
  text-align: center;
  font-size: 1.25rem;
  font-weight: 400;
  text-decoration: none !important;
  border-bottom: none;
  transition: all 0.2s ease-out;
  max-width: 50%;

  &.primary {
    color: var(--dark);
    background-color: var(--brand);

    &:hover {
      color: black;
      background-color: white;
    }
  }

  &.secondary {
    background: none;
    color: var(--brand);

    &::after {
      content: "›";
      font-size: 24px;
      margin-left: 5px;
    }

    &:hover {
      color: white;
    }
  }
}

@media only screen and (max-width: 480px) {
  .ActionButton {
    max-width: 100%;
    width: 100%;
    display: block;
    white-space: nowrap;
  }
}

/* AppList */

.AppList {
  display: grid;
  padding: 0;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-gap: 16px;

  .item {
    list-style: none;

    img {
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 6px 14px rgb(20 20 20 / 8%);
    }
  }
}

/* Community */

.Community .content {
  max-width: 900px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;

  .firstP img {
    float: left;
    width: 56px;
    height: 56px;
    margin-right: 20px;
  }
}

@media only screen and (max-width: 480px) {
  .Community .Heading {
    width: 100%;
    padding: 0 1rem;
    margin-bottom: 1.5rem;
  }
}

@media only screen and (min-width: 481px) and (max-width: 960px) {
  .Community .Heading {
    width: 100%;
    padding: 0 4rem;
    margin-bottom: 1.5rem;
  }

  .Community .AppList {
    width: 100%;
    max-width: 500px;
    margin: 2rem auto;
  }
}

@media only screen and (min-width: 961px) {
  .Community .column.first {
    border-right: 1px solid var(--ifm-table-border-color);
  }
}

/* Cross Platform */

.CrossPlatform {
  svg {
    max-width: 400px;
    margin: -20px 0;

    text {
      fill: var(--ifm-color-content-secondary);
    }
  }
}

@media only screen and (max-width: 960px) {
  .CrossPlatform .TwoColumns {
    grid-gap: 2rem;
  }

  .CrossPlatform svg {
    max-width: 100%;
    margin: 0 auto;
  }
}

@media only screen and (min-width: 481px) and (max-width: 960px) {
  .CrossPlatform .column.last {
    width: 86%;
    margin: 0 auto;
    text-align: center;
  }
}

/* Fast Refresh */

/* Make video flush with the bottom */
.FastRefresh {
  margin-bottom: -50px;
}

/* Get rid of extra padding at the bottom of the video */
.FastRefresh .column.last {
  margin-bottom: -6px;
}

@media only screen and (max-width: 480px) {
  .FastRefresh .column.last {
    padding: 0;
  }

  .FastRefresh video {
    width: 100%;
  }
}

@media only screen and (min-width: 481px) and (max-width: 960px) {
  .FastRefresh .TwoColumns {
    grid-gap: 2rem;
  }

  .FastRefresh .column.last {
    width: 100%;
    padding: 0;
  }

  .FastRefresh video {
    width: 100%;
  }
}

@media only screen and (min-width: 961px) {
  /* Give video more space than text */
  .FastRefresh .TwoColumns {
    grid-template-columns: 2fr 1fr;
  }

  /* Make video flush with top of section */
  .FastRefresh .last {
    margin-top: -50px;
  }

  /* Need to set video height so it'll fit */
  .FastRefresh video {
    height: 340px;
    /* width: 100%; */
  }
}

/* Get Started */

.GetStarted,
.GetStarted p {
  color: white;
}

.GetStarted .Heading {
  color: var(--brand);
  text-align: center;
}

.GetStarted .content {
  max-width: 900px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.GetStarted .steps {
  align-self: center;
}

.GetStarted .steps li {
  font-size: 28px;
  margin-bottom: 8px;
}

.GetStarted .steps li p {
  font-size: 17px;
}

.GetStarted .terminal {
  display: flex;
  flex-direction: column;
  border-left: 1px solid gray;
  border-right: 1px solid gray;
  border-top: 1px solid gray;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  padding: 30px 30px 0px 30px;
  width: 600px;
  position: relative;
}

.GetStarted .terminal::before {
  content: "○ ○ ○";
  color: gray;
  font-size: 16px;
  position: absolute;
  left: 15px;
  top: 5px;
}

.GetStarted code {
  color: white;
  font-size: 18px;
  position: relative;
  background: none;
  border: 0;
}

.GetStarted code:first-child::before {
  content: ">";
  position: absolute;
  left: -13px;
  color: gray;
}

@media screen and (max-width: 760px) {
  .GetStarted .content {
    width: 80%;
  }

  .GetStarted .steps li {
    margin-left: -1rem;
  }

  .GetStarted .terminal {
    width: 100%;
  }
}

/* Header Hero */

.HeaderHero {
  padding-top: 20px;
}

.HeaderHero .TwoColumns .column {
  max-width: initial;
}

.HeaderHero .socialLinks {
  display: flex;
  justify-content: flex-end;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 36px;

  .github-button {
    margin-right: 1rem;
    margin-left: 1rem;
  }
}

.HeaderHero .TwoColumns {
  align-items: center;
}

.HeaderHero .title {
  font-size: 84px;
  color: var(--brand);
  line-height: 1;
  margin-top: 0;
  margin-bottom: 20px;
  font-weight: 500;
}

.HeaderHero .tagline {
  font-size: 36px;
  line-height: 1.3;
  color: white;
  font-weight: 500;
}

.HeaderHero .buttons {
  margin-top: 40px;
}

.HeaderHero .image {
  display: flex;
  align-items: center;
  justify-content: center;
}

@media only screen and (min-width: 961px) {
  .HeaderHero .TwoColumns {
    grid-template-columns: 3fr 1fr;
  }

  .HeaderHero .TwoColumns .column.left {
    padding-right: 0;
  }

  .HeaderHero .TwoColumns .column.right {
    padding-left: 0;
  }
}

@media only screen and (min-width: 481px) and (max-width: 960px) {
  .HeaderHero .column.first {
    display: flex;
    justify-content: center;
  }
  .HeaderHero .column.last {
    text-align: center;
  }
}

@media only screen and (max-width: 760px) {
  .HeaderHero .title {
    font-size: 60px;
  }

  .HeaderHero .tagline {
    font-size: 30px;
  }

  .HeaderHero .socialLinks {
    margin-top: -2rem;
  }
}

/* Heading */

.Heading {
  font-size: 25px;
  color: var(--ifm-font-color-base);
  line-height: 1.2;
  margin-top: 0;
  margin-bottom: 20px;
  font-weight: 700;
}

/* Home page */

.HomePage {
  width: 100%;
  overflow-x: hidden;
}

/* Logo Animation */

.LogoAnimation {
  width: 350px;
}

@media only screen and (max-width: 760px) {
  .LogoAnimation {
    width: 100%;
  }
}

.LogoAnimation .screen {
  transition: all 850ms ease-in-out;
  stroke-opacity: 0;
  transform: scale(2.25, 1.33) rotate(0);
  stroke-width: 5px;
}

.LogoAnimation .background {
  fill: var(--dark);
}

.LogoAnimation .logoInner {
  transform: scale(1);
  transition: all 850ms ease-in-out;
  transition-delay: 50ms;
}

.LogoAnimation.mobile .logoInner,
.LogoAnimation.mobile2 .logoInner {
  transform: scale(0.4);
}

.LogoAnimation.desktop .logoInner {
  transform: scale(0.5);
}

.LogoAnimation.laptop .logoInner {
  transform: scale(0.35);
}

.LogoAnimation.full .screen {
  stroke-opacity: 0;
  transform: scale(2.25, 1.33) rotate(0);
  opacity: 1;
}

.LogoAnimation.mobile .screen {
  stroke-opacity: 1;
  transform: scale(1) rotate(0);
  opacity: 1;
  stroke-width: 5px;
}

.LogoAnimation.desktop .screen {
  stroke-opacity: 1;
  transform: scale(1.125, 1.1) rotate(-90deg);
  opacity: 1;
  stroke-width: 8px;
}

.LogoAnimation.laptop .screen {
  stroke-opacity: 1;
  transform: scale(0.83) rotate(-90deg);
  opacity: 1;
  stroke-width: 5px;
}

.LogoAnimation.mobile2 .screen {
  stroke-opacity: 1;
  opacity: 1;
  stroke-width: 5px;
  transform: scale(1) rotate(-180deg);
}

.LogoAnimation.full2 .screen {
  stroke-opacity: 0;
  transform: scale(2.25, 1.33) rotate(-180deg);
}

.LogoAnimation:not(.mobile):not(.mobile2) .speaker {
  opacity: 0;
  transform: scaleX(0);
}

.LogoAnimation:not(.desktop) .stand {
  transform: scaleX(0);
}

.LogoAnimation:not(.laptop) .base {
  transform: scaleX(0);
}

.LogoAnimation .speaker,
.LogoAnimation .stand,
.LogoAnimation .base {
  transition: all 850ms ease-in-out;
}

/* Native Apps */

.NativeApps {
  overflow: hidden;
}

@media only screen and (max-width: 960px) {
  .NativeApps .column.last {
    max-height: 300px;
  }
}

@media only screen and (min-width: 481px) and (max-width: 960px) {
  .NativeApps .column.last {
    width: 66.7%;
    margin: 0 auto;
  }
}

@media only screen and (min-width: 961px) {
  .NativeApps {
    max-height: 400px;
  }

  /* Correct for whitespace in the image of phones */
  .NativeApps .column.left {
    margin-top: -25px;
  }
}

/* Native Code */

.NativeCode .column.last {
  margin-bottom: -50px;
}

.NativeCode pre {
  margin: 0;
}

.NativeCode .prism-code {
  border-radius: 0;
  font-size: 80%;
  background-color: #282c34;
}

@media only screen and (max-width: 480px) {
  .NativeCode .column.last {
    width: 100%;
    padding: 0;
    overflow-x: hidden;
  }

  .NativeCode .prism-code {
    font-size: 10px;
    padding: 1.25rem 1.25rem;
  }
}

@media screen and (min-width: 481px) and (max-width: 960px) {
  .NativeCode .TwoColumns {
    grid-gap: 2rem;
  }

  .NativeCode .column.last {
    width: 100%;
    padding: 0;
    background-color: var(--dark);
    height: 28rem;
    overflow-y: scroll;
  }

  .NativeCode .prism-code {
    width: 30rem;
    margin: 0 auto;
    padding: 1.25rem 0rem;
  }
}

@media only screen and (min-width: 961px) {
  .NativeCode .TwoColumns .column.right {
    /* Make flush with top and bottom */
    margin-top: -50px;
    /* Get rid of default left padding */
    padding-left: 0;
  }

  .NativeCode .column.right .prism-code {
    /* Bleed background into the right */
    margin-right: -9999px;
    padding: 16px 1.5rem;
    height: 460px;
  }
}

/* Native Development */

.NativeDevelopment {
  overflow-y: hidden;
}

.NativeDevelopment .dissection {
  position: relative;
  margin-top: -50px;
}

.NativeDevelopment .dissection img {
  position: absolute;
  left: 0;
  top: 0;
}

@media only screen and (max-width: 960px) {
  .NativeDevelopment .TwoColumns {
    grid-gap: 2rem;
  }
}

@media only screen and (max-width: 480px) {
  .NativeDevelopment .dissection {
    height: 350px;
  }
}

@media only screen and (min-width: 481px) and (max-width: 960px) {
  .NativeDevelopment .dissection {
    height: 450px;
  }
}

@media only screen and (min-width: 961px) {
  .NativeDevelopment .dissection {
    height: 300px;
  }
}

/* Section */

.Section {
  width: 100%;
  padding-top: 50px;
  padding-bottom: 50px;
  overflow-x: hidden;
}

.Section + .Section {
  border-top: 1px solid var(--ifm-table-border-color);
}

.Section.tint {
  background-color: var(--ifm-menu-color-background-active);
}

.Section.dark {
  background-color: var(--dark);
}

.Section p a {
  @extend %link-style;
}

html[data-theme="dark"] .Section p a {
  @extend %link-style-dark;
}

/* VideoContent */

.VideoContent {
  .twitter-follow-button {
    margin-top: 1.5rem;
  }
}

@media only screen and (max-width: 960px) {
  .VideoContent .TwoColumns {
    grid-gap: 2rem;
  }

  .VideoContent .column.last {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  /*
   * If the full-width video won't fit, make it full-width.
   * https://jameshfisher.com/2017/08/30/how-do-i-make-a-full-width-iframe/
   */
  .VideoContent .vidWrapper {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
  }

  .VideoContent iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

@media only screen and (min-width: 961px) {
  /* Give more width for the video */
  .VideoContent .TwoColumns {
    grid-template-columns: 1fr 2fr;
  }

  .VideoContent iframe {
    width: 560px;
    height: 315px;
  }
}

/* Two columns */

.TwoColumns {
  display: grid;
}

.TwoColumns .column {
  width: 100%;
}

.TwoColumns .column.first {
  grid-area: first;
}

.TwoColumns .column.last {
  grid-area: last;
}

@media only screen and (min-width: 961px) {
  .TwoColumns {
    max-width: 900px;
    margin: 0 auto;
    grid-template-columns: repeat(2, 1fr);
    grid-template-areas: "first last";
  }

  .TwoColumns.reverse {
    grid-template-areas: "last first";
  }

  .TwoColumns .column {
    max-width: 450px;
  }

  .TwoColumns .column.left {
    padding-right: 50px;
  }

  .TwoColumns .column.right {
    padding-left: 50px;
  }
}

@media only screen and (max-width: 960px) {
  .TwoColumns,
  .TwoColumns.reverse {
    grid-template-columns: 1fr;
    grid-template-areas: "first" "last";
  }

  .TwoColumns.TwoFigures {
    grid-template-columns: repeat(2, 1fr);
    grid-template-areas: "first" "last";
  }

  .TwoColumns .column {
    padding: 0 4rem;
  }
}

@media only screen and (max-width: 480px) {
  .TwoColumns .column {
    padding: 0 1.25rem;
  }

  .TwoColumns.TwoFigures {
    grid-template-columns: 1fr;
    grid-template-areas: "first" "last";
  }
}
