/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

@import "shared";

:root {
  --brand: #61dafb;
  --dark: #282c34;
  --deepdark: #20232a;
  --light: #373940;
  --text: #1a1a1a;
  --subtle: #636363;
  --divider: #ececec;
  --tintColor: #f7f7f7;
  --rn-note-background: rgba(255, 229, 100, 0.25);
  --ifm-font-family-base:
    "Optimistic Display", system-ui, -apple-system, sans-serif;
  --ifm-color-primary: #06bcee;
  --ifm-font-size-base: 17px;
  --ifm-spacing-horizontal: 16px;
  --ifm-navbar-item-padding-horizontal: 18px;
  --ifm-menu-link-padding-horizontal: 0;
  --ifm-toc-padding-vertical: 6px;
  --ifm-toc-link-color: var(--ifm-color-emphasis-700);
  --ifm-blockquote-color: var(--ifm-font-color-base);
  --ifm-blockquote-font-size: 16px;
  --ifm-blockquote-border-radius: var(--ifm-global-radius);
  --ifm-table-head-color: var(--subtle);
  --ifm-link-hover-decoration: none;
  --ifm-navbar-background-color: var(--deepdark);
  --ifm-pre-line-height: 1.5;
  --ifm-tabs-padding-vertical: 6px;
  --ifm-color-warning: #ffe564;
  --ifm-alert-color: var(--ifm-font-color-base);
  --ifm-pagination-nav-color-hover: var(--ifm-color-emphasis-300);
  --ifm-navbar-sidebar-width: 100%;
  --ifm-hr-border-color: var(--docusaurus-collapse-button-bg-hover);
  --ifm-table-head-background: var(--ifm-color-emphasis-100);
  --ifm-table-border-color: var(--ifm-toc-border-color);
  --ifm-table-cell-padding: 10px;
  --ifm-table-stripe-background: rgba(0, 0, 0, 0.02);

  @media (min-width: 340px) {
    --ifm-navbar-sidebar-width: 340px;
  }

  --logo: #087ea4;
  --home-hero-floor-background: rgb(236, 248, 250);
  --home-hero-floor-background-bottom: white;
  --home-button-primary: #087ea4;
  --home-button-primary-text: white;
  --home-button-primary-hover: #4e98b6;
  --home-button-secondary: white;
  --home-button-secondary-text: #404756;
  --home-button-secondary-border: #bcc1cd;
  --home-button-secondary-hover: #f8f9fa;
  --home-hero-devices-border: #5e687e;
  --home-hero-devices-stop: #66728b;
  --home-hero-devices-skeleton-element: #cfe6ee;
  --home-hero-devices-background: #fff;
  --home-hero-devices-icon: #000;
  --home-hero-devices-icon-border: #e5e7eb;
  --home-hero-floor: #acddec;
  --home-hero-grid-icon-background: #f8f9fa;
  --home-hero-grid-icon: #e1e4e8;
  --home-hero-grid-grid: #ced4da;
  --home-section-top: white;
  --home-section-bottom: #efeff2;
  --home-secondary-text: #404756;
  --home-feature-background-1: #f1dbfc;
  --home-feature-background-2: #e1eefc;
  --home-feature-background-3: #d4f3e7;
  --home-border: #e5e7eb;
  --home-background: #fff;
  --home-secondary-background: #f6f7f9;
  --home-text: #1c1e21;
  --home-code-red: #d73a49;
  --home-code-purple: #6f42c1;
  --home-code-green: #22863a;
  --home-code-diff-removed: #f8514926;
  --home-code-diff-removed-word: #f8514966;
  --home-code-diff-added: #2ea04326;
  --home-code-diff-added-word: #2ea04366;

  @media (max-width: 900px) {
    --home-hero-grid-icon: transparent;
    --home-hero-grid-icon-background: transparent;
  }
}

body {
  font-family: var(--ifm-font-family-base);
}

html[data-theme="light"] {
  --ifm-code-background: rgba(0, 0, 0, 0.06);
  --docsearch-container-background: rgba(32, 35, 42, 0.6);
  --ifm-link-color: #357da1;
}

html[data-theme="dark"] {
  --ifm-code-background: rgba(255, 255, 255, 0.06);
  --ifm-toc-border-color: var(--dark);
  --ifm-color-emphasis-300: var(--dark);
  --ifm-table-head-background: var(--deepdark);
  --subtle: #a7a7a7;
  --ifm-table-head-color: var(--subtle);

  *[class^="DocSearch"] {
    --docsearch-searchbox-background: var(--ifm-background-color);
    --docsearch-modal-background: var(--deepdark);
    --docsearch-footer-background: var(--dark);
    --docsearch-key-gradient: var(--deepdark);
    --docsearch-key-shadow:
      inset 0 -2px 0 0 var(--light), inset 0 0 1px 1px var(--light),
      0 1px 2px 1px var(--ifm-table-border-color);
    --docsearch-container-background: rgba(0, 0, 0, 0.6);
  }

  --logo: #58c4dc;
  --home-hero-floor-background: #151517;
  --home-hero-floor-background-bottom: #1b1b1d;
  --home-button-primary: #58c4dc;
  --home-button-primary-text: #1b1b1d;
  --home-button-primary-hover: #71d6ec;
  --home-button-secondary: #1b1b1d;
  --home-button-secondary-text: #f6f7f9;
  --home-button-secondary-border: #4e5668;
  --home-button-secondary-hover: #2b2b2d;
  --home-hero-devices-border: #404756;
  --home-hero-devices-stop: #4a5160;
  --home-hero-devices-skeleton-element: #404756;
  --home-hero-devices-background: #242426;
  --home-hero-devices-icon: #fff;
  --home-hero-devices-icon-border: #404756;
  --home-hero-floor: #30363d;
  --home-hero-grid-icon-background: #1e2025;
  --home-hero-grid-icon: #282c36;
  --home-hero-grid-grid: #30363d;
  --home-section-top: #1b1b1d;
  --home-section-bottom: #111113;
  --home-secondary-text: #c0c1c4;
  --home-feature-background-1: #333049;
  --home-feature-background-2: #1c3950;
  --home-feature-background-3: #1d413e;
  --home-border: #404756;
  --home-background: #242426;
  --home-secondary-background: #2c2c2e;
  --home-text: #e3e3e3;
  --home-code-red: #f46b78;
  --home-code-purple: #a77cf7;
  --home-code-green: #74e68f;
  --home-code-diff-removed: #f8514926;
  --home-code-diff-removed-word: #f8514966;
  --home-code-diff-added: #2ea04326;
  --home-code-diff-added-word: #2ea04366;

  @media (max-width: 900px) {
    --home-hero-grid-icon: transparent;
    --home-hero-grid-icon-background: transparent;
  }
}

:root {
  --doc-sidebar-width: 266px !important;
}

::marker {
  color: var(--ifm-font-color-secondary);
}

/* Font */

@font-face {
  font-family: "Optimistic Display";
  src: url("https://facebookmicrosites.github.io/design/public/fonts/OptimisticDisplayLight-199be98cf48e5b4c688356b08a02362c.woff2")
    format("woff2");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "Optimistic Display";
  src: url("https://facebookmicrosites.github.io/design/public/fonts/OptimisticDisplayRegular-b0e4e99f91efd0021c3ab8e4df0e6e1b.woff2")
    format("woff2");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "Optimistic Display";
  src: url("https://facebookmicrosites.github.io/design/public/fonts/OptimisticDisplayBold-3a50548145f36de582c3b36d9626f4d6.woff2")
    format("woff2");
  font-weight: 700;
  font-style: normal;
}

/* Content */

.main-wrapper {
  max-width: 1400px;
  width: 1400px;
  align-self: center;
  font-weight: 300;

  div[class*="docRoot"] {
    justify-content: center;
  }

  h1[class^="docTitle"] {
    font-size: 2.8rem;
  }

  .admonition-content {
    a {
      text-decoration: none;
    }
  }
}

/*
Layer needed to override utility rules using !important in the Infima layer
See also https://css-tricks.com/css-cascade-layers/#aa-important-layers
*/
@layer docusaurus.infima.importantOverrides {
  main[class^="docMainContainer"] > .container {
    padding-top: 2rem !important;
  }
}

div[class^="generatedIndexPage"] {
  max-width: 1080px !important;
}

@media (max-width: 1416px) {
  .main-wrapper {
    max-width: 100%;
    width: 100%;
  }
}

@media (max-width: 1320px) and (min-width: 997px) {
  .container {
    max-width: calc(var(--ifm-container-width) - var(--doc-sidebar-width) / 2);
  }
}

@media (min-width: 997px) {
  main[class^="docMainContainer"] {
    max-width: calc(100% - var(--doc-sidebar-width) + 44px);
  }
}

hr {
  border: 0;
  height: 0.01rem;
  background-color: var(--ifm-table-border-color);
}

.markdown {
  h2:first-child {
    margin-top: 0;
  }

  code {
    border-color: transparent;
    vertical-align: initial;
    font-size: var(--ifm-code-font-size) !important;
  }

  blockquote {
    > :last-child {
      margin-bottom: 0;
    }

    background-color: var(--rn-note-background);
    border-left: 8px solid var(--ifm-color-warning);
    padding: 15px 30px 15px 15px;
    border-radius: var(--ifm-blockquote-border-radius);

    code {
      background-color: rgba(0, 0, 0, 0.07);
    }

    a code {
      background-color: transparent;
      white-space: pre;
    }
  }

  a {
    @extend %link-style;

    code {
      background-color: transparent;
      font-size: 90%;
      padding: 0 6px;
      top: -1px;
      position: relative;
    }

    .hermes-logo {
      float: right;
      margin: -30px 4px 0;
    }

    @media screen and (max-width: 960px) {
      .hermes-logo {
        margin: 0 4px 30px;
        width: 100%;
      }
    }
  }

  strong {
    font-weight: 600;
  }

  table {
    display: table;
    width: 100%;
    table-layout: fixed;

    thead tr {
      border: 0;
    }

    tr th {
      padding: 6px var(--ifm-table-cell-padding);
      font-size: 80%;
      text-align: start;
    }

    tr td {
      font-size: 90%;
      line-height: 1.3em;
      text-align: start;

      code {
        display: inline-block;
        line-height: 1.2em;
        vertical-align: top;
        word-break: break-word;
      }

      ul {
        margin: 8px 0 0 0;
        padding: 4px 0 4px 20px;
      }
    }

    hr {
      margin: 12px 0;
    }

    .wideColumn {
      width: 128px;
    }

    .widerColumn {
      width: 180px;
    }

    .table-heading {
      font-weight: var(--ifm-table-head-font-weight);
      border-bottom: 2px solid var(--ifm-table-border-color);
    }

    div[class*="codeBlockContainer"] {
      &,
      & pre {
        margin-bottom: 0;
      }

      pre code {
        padding: 12px 16px;
        white-space: break-spaces;
      }

      button {
        display: none;
      }
    }
  }

  figure {
    text-align: center;
    padding: 8px;

    figcaption {
      padding: 8px;
      font-size: 0.9rem;
      color: var(--subtle);

      code {
        color: var(--subtle);
      }
    }
  }

  .docusaurus-highlight-code-line {
    background-color: var(--light);
  }

  div[class*="codeBlockContainer"] {
    box-shadow: none;
    border-radius: 8px;

    pre {
      border-radius: 8px;
    }

    button {
      border-color: var(--light);
      background: var(--deepdark);
    }
  }

  div[class*="codeBlockContent"] {
    display: grid;
    min-width: 100%;
  }

  div[class*="codeBlockLines"] {
    font-size: 80%;
  }

  div[class*="codeBlockTitle"] {
    color: var(--subtle);
    background-color: var(--ifm-color-emphasis-300);
  }

  .prism-code {
    @extend %scrollbar-style;
  }

  .tabs {
    border-bottom: 1px solid var(--ifm-table-border-color);
    color: var(--ifm-font-color-base);

    .tabs__item {
      font-weight: normal;
      font-size: 15px;
      color: var(--ifm-font-color-base);
      user-select: none;

      &:hover {
        border-radius: var(--ifm-global-radius) var(--ifm-global-radius) 0 0;
      }

      &.tabs__item--active {
        font-weight: 700;
      }
    }
  }
}

@media only screen and (max-width: 996px) {
  .markdown table {
    display: block;

    tr td code {
      word-break: normal;
    }
  }
}

html[data-theme="dark"] {
  .markdown {
    a {
      @extend %link-style-dark;
    }

    blockquote {
      code {
        background-color: rgba(255, 255, 255, 0.09);
      }

      a code {
        background-color: transparent;
      }
    }

    .prism-code {
      @extend %scrollbar-style-dark;
    }

    div[class*="codeBlockTitle"] {
      color: var(--docsearch-muted-color);
      background-color: var(--deepdark);
    }
  }
}

div[class^="docRoot"] .hash-link,
article[itemprop="blogPost"] .hash-link,
.mdx-page .hash-link,
html[data-theme="dark"] div[class^="docRoot"] .hash-link,
html[data-theme="dark"] article[itemprop="blogPost"] .hash-link,
html[data-theme="dark"].mdx-page .hash-link {
  @extend %hash-link-style;
}

article .badge {
  font-weight: 500;
}

html[data-theme="dark"] article .badge {
  background: var(--deepdark);
  border-color: var(--deepdark);
  color: var(--ifm-font-color-base);
}

/* Admonitions & notes */

.admonition,
.theme-admonition {
  font-size: var(--ifm-blockquote-font-size);
  margin-bottom: 1.5rem !important;

  div[class^="admonitionContent"] {
    a {
      border-bottom: 0;
    }
  }

  div[class^="admonitionHeading"] {
    font-size: 14px;
  }
}

.alert--warning {
  background-color: var(--rn-note-background);
}

.alert--secondary {
  --ifm-alert-background-color: var(--ifm-color-secondary-lightest);
  --ifm-alert-background-color-highlight: rgba(225, 227, 230, 0.7);
}

html[data-theme="dark"] .alert--secondary {
  --ifm-alert-background-color: var(--ifm-color-secondary-contrast-background);
  --ifm-alert-background-color-highlight: rgba(225, 227, 230, 0.15);
}

/* Home page */

.homepage {
  div[class*="codeBlockContainer"] {
    margin-bottom: 0;
    overflow: visible;

    .prism-code {
      font-size: 90%;
    }
  }
}

/* Version warning */

.theme-doc-version-banner {
  background-color: rgba(100, 215, 255, 0.3);
  border-style: none;
  color: var(--ifm-font-color-base);
  box-shadow: none;
  font-size: 15px;
  line-height: var(--ifm-pre-line-height);
  text-align: center;

  a {
    color: var(--ifm-font-color-base) !important;
    text-decoration: underline !important;
    text-decoration-thickness: 1px !important;
  }

  @layer docusaurus.infima.importantOverrides {
    .margin-top--md {
      margin-top: 0.33rem !important;
    }
  }
}

/* Sidebars */

html[data-theme="dark"] {
  @extend %scrollbar-style-dark;
}

/* Blog */

.avatar__name {
  font-weight: 600;
  display: inline-flex;
  gap: 4px;
  align-items: center;

  h2 {
    margin-bottom: 0;
  }
}

html[data-theme="dark"] .avatar__name a {
  @extend %link-style-dark;
}

.avatar__subtitle {
  margin-top: 0;
  font-weight: 500;
  color: var(--subtle);
}

article header h1 {
  font-size: 2.5rem !important;
  line-height: 3rem;
}

article header h2 {
  line-height: 3rem;

  a {
    color: var(--ifm-font-color-base);
    font-size: 2.5rem;

    &:hover,
    &:focus {
      color: var(--ifm-font-color-base);
      opacity: 0.8;
    }
  }
}

div[class^="blogPostData"] {
  color: var(--subtle);
}

.main-wrapper.blog-wrapper .container.margin-vert--lg .col.text--right a {
  padding: 8px 16px !important;
}

a[class*="tagRegular"] {
  padding: 6px 12px !important;

  &:hover {
    background: var(--ifm-menu-color-background-active);
    --docusaurus-tag-list-border: inherit;
  }
}

.blog-wrapper {
  .pagination-nav__item .pagination-nav__label:before,
  .pagination-nav__item--next .pagination-nav__label:after {
    display: none;
  }
}

/* Navbar */

.navbar {
  &.navbar--dark {
    background-color: var(--deepdark);
  }

  .navbar__inner {
    max-width: 1360px;
    margin: 0 auto;
  }

  .navbar__title {
    font-weight: 600;
    font-size: 18px;
  }

  .navbar__brand:hover {
    color: var(--brand);
  }

  .navbar__item {
    &.navbar__link {
      font-weight: 400;
      font-size: 17px;
      user-select: none;
    }

    &.dropdown:not(.dropdown--right) {
      a {
        font-weight: 400;
        font-size: 14px;
      }
      ul {
        /* Control navbar dropdown alignment */
        top: calc(100% + 0px);
        left: 0;
      }
    }

    &.dropdown--right {
      .dropdown__menu {
        min-width: 168px;
      }
    }
  }

  .dropdown > .navbar__link:after {
    margin-left: 8px;
    top: 1px;
    opacity: 0.75;
  }

  .navbar__logo {
    width: 38px;
    height: 34px;
    transition: transform 0.5s;
  }

  .navbar__brand:hover .navbar__logo {
    transform: rotate(180deg) scale(0.9);
  }

  .DocSearch-Button {
    border-radius: var(--ifm-global-radius);
    padding: 0 6px 0 10px;
    font-family: var(--ifm-font-family-base);

    .DocSearch-Search-Icon {
      width: 16px;
      margin-top: -1px;
    }
  }

  .DocSearch-Button-Placeholder,
  .DocSearch-Button-Key {
    font-size: 14px !important;
  }

  .DocSearch-Button-Key {
    padding-bottom: 0 !important;
  }

  .DocSearch-Button-Key svg {
    margin-bottom: 1px !important;
  }

  .dropdown__menu {
    min-width: 120px;

    a.dropdown__link--active {
      font-weight: 600;
    }
  }

  .navbar__items div[class^="navbarSearchContainer"] {
    padding-left: 14px;
  }

  .navbar-github-link {
    &:after {
      transition: opacity 0.2s;
      content: "";
      width: 24px;
      height: 24px;
      display: flex;
      background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23fff' d='M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
    }

    &:hover:after {
      opacity: 0.5;
    }
  }

  .navbar__link--active:after {
    content: "";
    display: flex;
    width: calc(100% + 40px);
    height: 4px;
    margin-top: -4px;
    margin-left: -20px;
    margin-right: -20px;
    position: relative;
    top: 18px;
    background: var(--brand);
  }

  .navbar-sidebar__back:hover {
    background: var(--ifm-code-background);
  }
}

/*
Config has themeConfig.navbar.style === "dark"
But the navbar sidebar should still support light theme
So we need to "revert" some CSS vars to not enforce dark mode
 */
.navbar--dark {
  --ifm-menu-link-sublist-icon-filter: inherit; /* reset to non-dark value */
  --ifm-menu-color: inherit;
  --ifm-navbar-background-color: var(--divider);
}

.navbar-sidebar {
  .navbar-sidebar__brand {
    background-color: var(--deepdark);
  }

  .navbar-sidebar__items {
    > .navbar-sidebar__item.menu {
      > ul.menu__list {
        margin: 4px;
      }
    }

    .menu__list-item a {
      color: var(--ifm-color-emphasis-700);
      padding: 4px 12px;
    }

    .menu__list-item-collapsible a[class*="menuLinkText"] {
      font-weight: 600;
      font-size: 14px;
      background: var(--ifm-background-color) !important;
    }
  }

  .navbar-github-link {
    &:after {
      content: "GitHub";
      background-image: none;
      height: 20px;
    }

    &:hover:after {
      opacity: 1;
    }
  }

  .navbar__link--active:after {
    display: none;
  }
}

html[data-theme="dark"] {
  .navbar-sidebar {
    background: var(--ifm-background-color);
  }

  .navbar button[class*="toggleButton"]:hover {
    background: var(--ifm-color-emphasis-200);
  }
}

@media (max-width: 1200px) {
  .navbar {
    .navbar__item.navbar__link {
      font-size: 15px;

      &.navbar__link--active:after {
        top: 20px;
      }
    }

    .DocSearch-Button {
      padding: 0 12px;
      max-width: 40px;
    }

    .DocSearch-Button-Key,
    .DocSearch-Button-KeySeparator,
    .DocSearch-Button-Placeholder {
      display: none;
    }
  }
}

/* Sidebar */

aside[class^="theme-doc-sidebar-container"] {
  margin-right: 16px;

  .thin-scrollbar {
    @extend %scrollbar-style;
  }

  .menu__list .menu__list {
    margin-top: 0;
  }

  .menu__list .menu__link--sublist,
  a[class*="menu__link--sublist-caret"] {
    font-size: 16px !important;
    font-weight: 700 !important;
    color: var(--ifm-color-content-secondary) !important;

    &:after {
      display: none;
    }

    &[aria-expanded]:after {
      display: block;
      background-size: 1.66rem 1.66rem;
      margin-right: -6px;
      margin-top: -1px;
    }
  }

  .menu__list .menu__list .menu__link--sublist,
  a[class*="menu__link--sublist-caret"] {
    font-weight: 700 !important;
    font-size: 15px !important;
  }

  .menu__list-item .menu__list-item .menu__list-item {
    padding-left: 8px !important;
  }

  .menu__link {
    justify-content: space-between;
  }

  .theme-doc-sidebar-item-category {
    margin-bottom: 6px;

    .menu__list .menu__list-item-collapsible {
      margin-top: 6px;
    }
  }

  .menu__list-item-collapsible {
    .menu__caret {
      padding: 0 6px;

      &:before {
        display: block;
        background-size: 1.66rem 1.66rem;
        margin-top: -1px;
      }
    }

    &:hover {
      background: none !important;

      .menu__link--sublist-caret:hover {
        background: var(--ifm-menu-color-background-hover) !important;
      }
    }
  }

  @media only screen and (max-width: 1120px) {
    --doc-sidebar-width: 240px;
  }
}

@media only screen and (min-width: 997px) {
  aside[class^="theme-doc-sidebar-container"] {
    .menu {
      padding: 12px;
    }
  }
}

@media only screen and (max-width: 996px) {
  aside[class^="theme-doc-sidebar-container"] {
    width: 0 !important;
    min-width: 0 !important;
    margin-right: 0;
  }
}

.menu__list {
  margin-bottom: 8px;

  &.blog-menu__list {
    font-size: 14px;
    font-weight: 300;

    a {
      line-height: 20px;
    }

    .menu__link--active {
      font-weight: 600;
    }
  }

  .menu__list-item {
    margin-bottom: 2px;
    margin-top: 2px;
    user-select: none;
  }

  .menu__link--sublist {
    font-size: 15px;
    padding: 4px 12px !important;
    color: var(--light);

    &:after {
      background-size: 1.66rem 1.66rem;
      margin-right: -6px;
    }
  }

  .menu__link--sublist-caret {
    font-size: 17px;
  }

  .menu__list {
    .menu__link {
      line-height: 20px;
      font-size: 13px;
      padding: 4px 12px !important;
      font-weight: 300;
      color: var(--ifm-font-color-base);
    }

    .menu__link--sublist {
      font-size: 14px !important;
    }

    .menu__list .menu__link--active {
      padding-left: 12px !important;
    }
  }

  .menu__link.menu__link--active {
    border-left-color: var(--ifm-menu-color-active) !important;
    border-left-width: 0;
  }

  a[href].menu__link.menu__link--active:not(.menu__link--sublist) {
    background: var(--ifm-code-background);
    padding-left: 8px !important;
    border-left-width: 4px;
    border-left-style: solid;
  }

  a[href="#"].menu__link.menu__link--active {
    border-left-width: 0;
  }

  .menu__list-item-collapsible {
    .menu__link--active {
      border-left-color: transparent !important;
    }

    .menu__link {
      font-weight: 600;
      font-size: 15px;
      color: var(--subtle);
      padding-left: 8px !important;

      .menu__link {
        padding-left: 12px !important;
      }
    }
  }

  .menu__list-item--collapsed .menu__list {
    margin-bottom: 0;
  }
}

.menu--responsive .menu__button {
  right: 1.3rem;
  bottom: 1.3rem;
}

html[data-theme="dark"] .menu {
  @extend %scrollbar-style-dark;

  .menu__list > .menu__list-item > .menu__link--sublist {
    color: var(--ifm-color-emphasis-400);
  }

  .menu__list .menu__list .menu__link--sublist {
    color: var(--subtle);
  }
}

/* TOC */

div[class^="tableOfContents"] {
  min-width: 100%;
  width: 100%;

  @extend %scrollbar-style;
}

.table-of-contents {
  li {
    margin: 7px var(--ifm-toc-padding-vertical);
  }

  ul {
    padding-left: 0.35rem;

    li {
      margin: 4px var(--ifm-toc-padding-vertical);
    }
  }

  .table-of-contents__link {
    font-size: 12px;
    display: block;

    code {
      background: none;
      padding: 0;
      border: 0;
    }

    &:hover {
      color: var(--ifm-link-color);

      code {
        color: var(--ifm-link-color);
      }
    }
  }

  .table-of-contents__link--active {
    font-weight: 500;
    margin-left: -16px;
    padding-left: 12px;
    color: var(--ifm-font-color-base);
    border-left: 4px solid var(--ifm-link-color);

    code {
      color: var(--ifm-font-color-base);
      font-weight: 700;
      white-space: nowrap;
    }

    &:hover {
      color: var(--ifm-font-color-base);

      code {
        color: var(--ifm-font-color-base);
      }
    }
  }
}

html[data-theme="dark"] {
  .table-of-contents {
    .table-of-contents__link--active {
      color: var(--ifm-link-color);

      &:hover {
        color: var(--ifm-link-color);

        code {
          color: var(--ifm-link-color);
        }
      }
    }
  }
}

html[data-theme="dark"] div[class^="tableOfContents"] {
  @extend %scrollbar-style-dark;
}

button[class*="tocCollapsibleButton"] {
  color: var(--ifm-color-content-secondary);
}

.navbar-sidebar {
  background: var(--ifm-background-surface-color);
}

.navbar-sidebar__back {
  color: var(--ifm-color-content-secondary);
  background: var(--ifm-footer-background-color);
}

.card {
  padding: 1.5rem !important;
  border: 1px solid var(--ifm-color-emphasis-300) !important;
  box-shadow: none !important;
  transition:
    background-color 0.2s ease-in-out,
    border-color 0.2s ease-in-out !important;
  background-color: var(--ifm-background-color);

  &:hover {
    border-color: var(--ifm-color-emphasis-400) !important;
    background-color: var(--ifm-menu-color-background-hover);
  }
}

/* Doc Item footer metadata */

.theme-doc-footer {
  margin-top: 40px !important;
  margin-bottom: 16px !important;
  font-size: 15px;
  color: var(--subtle);

  a {
    @extend %button-link-style;

    svg {
      color: var(--ifm-color-primary);
    }
  }

  .lastUpdated {
    font-size: 13px;
    text-align: end;
  }

  time,
  strong {
    font-weight: 600;
  }

  .theme-last-updated {
    line-height: 18px;
  }
}

@media only screen and (min-width: 540px) and (max-width: 996px) {
  .theme-doc-footer .col {
    flex-basis: 50% !important;
    max-width: 50% !important;
  }
}

@media only screen and (max-width: 539px) {
  .theme-doc-footer .col {
    text-align: center;

    &:first-child {
      margin-bottom: 16px;
    }
  }
}

/* Pagination */

.pagination-nav {
  .pagination-nav__label {
    font-weight: 500;
  }

  .pagination-nav__sublabel {
    font-size: 15px;
  }

  .pagination-nav__link:hover {
    background: var(--ifm-menu-color-background-hover);
    border-color: var(--ifm-color-emphasis-300);
  }
}

/* Footer */

.footer {
  &.footer--dark {
    --ifm-footer-background-color: var(--deepdark);
  }

  .container {
    max-width: 900px;
  }

  .text--center {
    color: #858a96;
    font-size: 13px;
  }

  .footer__item {
    font-size: 14px;
    padding: 8px 0 0;
    min-height: 30px;

    a:hover svg {
      color: var(--brand);
    }
  }

  .footer__title {
    color: #858a96;
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 8px;
    text-transform: uppercase;
  }

  .footer__col {
    margin: 4px 0 12px 2%;
    padding-left: 0 !important;
  }

  .footer__link-item {
    line-height: 1.44;
    display: inline-block;
  }

  .footer__links {
    margin-bottom: 8px;
  }

  .footer__logo {
    height: 54px;
    max-width: 100%;
  }

  svg[class^="iconExternalLink"] {
    color: var(--subtle);
    right: -1px;
    width: 9px;
  }
}

@media only screen and (max-width: 996px) {
  .footer {
    .footer__col {
      text-align: center;
      flex-basis: 48% !important;
      max-width: 48% !important;
      margin-bottom: 28px;
    }

    .footer__links {
      padding: 0 20px;
    }
  }
}

@media only screen and (max-width: 500px) {
  .footer {
    .footer__col {
      flex-basis: 99% !important;
      max-width: 99% !important;
      margin-bottom: 24px;
      text-align: center;
    }
  }
}

/* Announcement banner */

:root {
  --docusaurus-announcement-bar-height: auto !important;
}

@media (min-width: 997px) {
  :root {
    --docusaurus-announcement-bar-height: auto !important;
  }
}

div[class*="announcementBar"][role="banner"] {
  border-bottom-color: var(--deepdark);

  button.close {
    svg {
      fill: white;
    }
  }
}

div[class*="announcementBarContent"] {
  line-height: 40px;
  font-size: 20px;
  font-weight: bold;
  padding: 8px 30px;

  a {
    text-decoration: underline;
    display: inline-block;
    color: var(--brand) !important;

    &:hover {
      color: var(--ifm-color-primary) !important;
    }
  }
}

@media only screen and (max-width: 768px) {
  div[class*="announcementBarContent"] {
    font-size: 18px;
  }
}

@media only screen and (max-width: 500px) {
  div[class*="announcementBarContent"] {
    font-size: 15px;
    line-height: 22px;
    padding: 6px 30px;
  }
}

/* Content banner */

.content-banner {
  background: #99d5e7;
  padding: 1em 1.2em;
  margin-bottom: 2em;
  position: relative;
  overflow: hidden;
  width: calc(100%);
  color: var(--dark);
  border-radius: var(--ifm-global-radius);
}

.content-banner p {
  width: calc(75% - 2em);
  margin: 0;
}

.content-banner-img {
  width: 25%;
  position: absolute;
  top: 2em;
  right: 2em;
}

.content-banner a:link,
.content-banner a:visited,
.content-banner a:hover,
.content-banner a:active,
.content-banner a:focus {
  border-bottom-width: 2px;
  background: none !important;
  color: #134484 !important;
  border-bottom-color: #134484 !important;
}

/* "Native Code Required" banner */

.banner-native-code-required {
  position: relative;
  overflow: hidden;
  background: #eeebfe;
  padding: 24px 48px 24px 24px;
  margin-bottom: 24px;
  border-left: 8px solid #6170af;
  border-radius: var(--ifm-alert-border-radius);

  h2,
  h3 {
    color: #6170af;
    margin-top: 0;

    &:before {
      content: " ";
      float: left;
      width: 32px;
      height: 32px;
      border-radius: 4px;
      background-color: #6170af;
      background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 23 21' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M0 18.036c.042.507.221 1.016.692 1.699.558.81 1.518 1.254 2.218.54.473-.481 5.582-9.335 8.045-12.69a.737.737 0 011.202 0c2.463 3.355 7.572 12.209 8.045 12.69.7.714 1.66.27 2.218-.54.55-.798.702-1.358.702-1.955 0-.407-7.958-15.086-8.76-16.308C13.593.297 13.357.042 12.053 0h-.994c-1.304.041-1.54.297-2.31 1.472C7.964 2.67.317 16.77 0 17.737v.299z' fill='%23fff'/%3E%3C/svg%3E%0A");
      background-repeat: no-repeat;
      background-position: center;
      background-size: 20px;
      margin-right: 12px;
      margin-top: -2px;
    }
  }

  p {
    margin-bottom: 0;
  }

  code {
    background-color: rgba(89, 91, 145, 0.12);
  }
}

html[data-theme="dark"] .banner-native-code-required {
  background: rgba(123, 132, 172, 0.35);
  border-left-color: #5d659a;

  h2,
  h3 {
    color: var(--ifm-font-color-base);
  }

  h2:before,
  h3:before {
    background-color: #5d659a;
  }

  code {
    background: rgba(165, 175, 219, 0.15);
  }
}

/* Color preview box */

.color-box {
  float: left;
  width: 24px;
  height: 24px;
  margin: 2px 8px 0 0;
  border: 0.05rem solid var(--deepdark);
  border-radius: 3px;
}

td .color-box {
  width: 20px;
  height: 20px;
}

/* Labels */

.label {
  display: inline-block;
  position: relative;
  font-size: 0.85rem;
  font-weight: 500;
  color: #fff;
  padding: 2px 12px;
  border-radius: 0 2px 2px 0;

  /* Label sharp end */
  &:before {
    content: "";
    position: absolute;
    top: 0;
    left: -12px;
    border-color: transparent;
    border-style: solid;
    border-width: 12px 12px 12px 0;
  }

  /* Label skeuomorphic hole */
  &:after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    width: 4px;
    height: 4px;
    border-radius: 2px;
    background: #fff;
  }

  /* Basic label */
  &.basic {
    border-radius: 3px;
    border-style: solid;
    border-width: 2px;

    &:before,
    &:after {
      display: none;
    }
  }

  /* Label variants */
  &.android {
    background: #a4c936;

    &:before {
      border-right-color: #a4c936;
    }
  }

  &.ios {
    background: #222;

    &:before {
      border-right-color: #222;
    }
  }

  &.tv {
    background: #6170af;

    &:before {
      border-right-color: #6170af;
    }
  }

  &.required {
    margin-left: 0;
    margin-right: 16px;
    color: #fa5035;
    border-color: #fa5035;
  }
}

html[data-theme="dark"] .label {
  color: var(--text);

  &:after {
    background: var(--ifm-background-color);
  }

  &.ios {
    background: #fff;

    &:before {
      border-right-color: #fff;
    }
  }

  &.required {
    color: #fa5035;
  }
}

/* Label inside other tags */

h2 .label {
  top: -6px;
  margin-left: 12px;
  padding: 3px 12px;
}

h3 .label {
  top: -3px;
  margin-left: 22px;
  line-height: 20px;
}

td .label {
  padding: 0 8px 0 10px;
  font-size: 0.7rem;
  margin-left: 14px;

  &:before {
    left: -8px;
    border-width: 10px 8px 10px 0;
  }

  &:after {
    top: 8px;
  }

  &.required {
    margin-left: 8px;
    letter-spacing: 0.02rem;
    padding: 0 6px;
    border-width: 1px;
    margin-right: 0;
  }

  &.two-lines {
    margin-left: 0;
    margin-top: 5px;

    &.ios,
    &.android,
    &.tv {
      margin-left: 8px;
    }
  }
}

/* Label as dot in the right sidebar */

.table-of-contents .label {
  position: absolute;
  padding: 0;
  width: 6px;
  height: 6px;
  border-radius: 100%;
  margin-left: 4px;
  margin-top: 7px;
  white-space: nowrap;
  overflow: hidden;
  color: transparent;
  float: initial;

  &.required {
    position: initial;
    margin-left: 0;
    margin-right: 6px;
    border-width: 3px;
    background: #fa5035;
  }

  /* Second dot shift */
  &:nth-child(3) {
    margin-left: 13px;
  }
}

/*
  Mark pages as "new" using href attribute:
  - to show badge in all versions use "PAGEID"
  - to restrict badge only to the current/default version use "docs/PAGEID"
  - to restrict badge only to the next version use "next/PAGEID"
*/

.menu__link[href*="NO_NEW_PAGES_CURRENTLY"]:after {
  content: "new";
  display: inline-block;
  position: relative;
  top: 1px;
  margin-right: -6px;
  margin-left: auto;
  padding: 3px 6px;
  border: 1px solid #86b300;
  border-radius: 3px;
  color: #86b300;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 10px;
  height: 10px;
  font-weight: 600;
}

/* Components grid */

article .component-grid {
  max-width: 800px;
  margin-bottom: 22px;

  .component {
    border: 1px solid var(--ifm-color-emphasis-500);
    border-radius: var(--ifm-global-radius);
    margin: 0 auto 24px;
    width: 100%;
    display: inline-block;
    transition: transform 0.2s ease-in-out;
    overflow: hidden;

    &:hover {
      transform: scale(1.05);
    }

    > a {
      display: block;
      height: 100%;
      border-bottom: none;
    }

    h3 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      padding: 0 10px;
      background-color: var(--home-button-primary);
      color: var(--home-button-primary-text);
      line-height: 36px;

      a {
        color: white;
      }
    }

    p {
      padding: 10px;
      font-size: 15px;
      margin: 2px;

      code {
        font-size: 15px;
        padding: 0 1px;
      }
    }
  }
}

html[data-theme="dark"] .component {
  border: 1px solid var(--ifm-color-emphasis-200);
}

@supports (display: grid) {
  article .component-grid {
    display: grid;
    grid-column-gap: 22px;
  }
}

@media only screen and (min-width: 768px) {
  article .component-grid.component-grid-border {
    border-bottom: 1px solid var(--ifm-table-border-color);
  }

  article .component {
    width: 30%;
    height: 150px;
    margin: 0 22px 22px auto;
    vertical-align: top;
  }

  @supports (display: grid) {
    article .component-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    @media only screen and (min-width: 1440px) {
      article .component-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    article .component {
      width: auto;
      height: auto;
      margin: 0;
    }
  }
}

/* Docs Rating Box */

.docsRating {
  display: inline-block;
  padding: 16px 30px 16px 40px;
  min-height: 66px;
  margin-top: 42px;
  margin-left: -32px;
  background-color: rgba(100, 215, 255, 0.3);
  text-align: center;
  color: #057594;
  line-height: 32px;
  font-weight: 500;
  border-radius: 0 var(--ifm-global-radius) var(--ifm-global-radius) 0;

  svg {
    height: 1.5em;
    width: 1.5em;
    margin: 0 0 0 6px;

    &:hover,
    &:focus {
      cursor: pointer;
      fill: var(--ifm-color-primary);
    }
  }

  .i_thumbsup {
    fill: #56a211;
    transform: translateY(0.25em);
    margin-left: 12px;
  }

  .i_thumbsdown {
    fill: #e9430f;
    transform: scale(-1, -1) translateY(-0.25em);
  }
}

html[data-theme="dark"] .docsRating {
  color: #cee9f3;
}

@media only screen and (max-width: 996px) {
  .docsRating {
    width: 100%;
    margin: 36px 0 0 !important;
    border-radius: var(--ifm-global-radius);
  }
}

/* Snack Player */

.snack-player {
  height: 635px;
  width: 100%;
  overflow: hidden;
  margin-bottom: 24px;
  border: 1px solid var(--ifm-table-border-color);
  border-radius: var(--ifm-global-radius);
}

.tabs-container .snack-player:last-child {
  margin-bottom: 0;
}

/* Two columns code block */

.two-columns {
  display: grid;
  gap: 0 2%;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr;
  grid-template-areas: ". .";

  div[class*="codeBlockContainer"] {
    overflow: auto;
  }

  div[class*="codeBlockLines"] {
    white-space: pre-wrap;
  }
}

@media only screen and (max-width: 996px) {
  .two-columns {
    gap: 0;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr;
    grid-template-areas: "." ".";
  }
}

/* Blog */

.blog-wrapper .main-wrapper {
  h3[class*="sidebarItemTitle"] {
    color: var(--ifm-font-color-secondary);
  }

  @layer docusaurus.infima.importantOverrides {
    .container.margin-vert--lg {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
    }
  }

  .container.margin-vert--lg {
    max-width: calc(100% - (var(--ifm-spacing-horizontal)) * 4);

    .col.text--right a {
      @extend %button-link-style;
      padding: 8px 32px;
    }

    a.margin-horiz--sm,
    a.padding-right--md {
      @extend %link-style;
      padding-right: 0 !important;
      margin-right: 16px;
    }

    .docusaurus-mt-lg {
      margin-top: 2rem;
    }

    footer {
      b {
        font-weight: 500;
      }

      .text--right b {
        font-weight: 700;
      }
    }
  }

  .container .row .col.col--3 {
    --ifm-col-width: calc(5 / 24 * 100%);
    padding: 0;
  }

  .container .row .col.col--7 {
    --ifm-col-width: calc(15 / 24 * 100%);
    padding: 24px 16px 24px 32px;

    &:last-child {
      --ifm-col-width: calc(19 / 24 * 100%);
    }
  }

  @layer docusaurus.infima.importantOverrides {
    article.margin-bottom--xl {
      margin-bottom: 2.5rem !important;
    }
  }

  aside nav {
    @extend %scrollbar-style;
  }
}

html[data-theme="dark"].blog-wrapper {
  .main-wrapper {
    aside nav {
      @extend %scrollbar-style-dark;
    }
  }
}

html[data-theme="light"].blog-wrapper {
  .main-wrapper {
    .container.margin-vert--lg {
      .col.text--right a:hover {
        color: var(--ifm-link-color);
      }
    }
  }
}

@media only screen and (max-width: 996px) {
  .container.margin-vert--lg .row .col.col--3 {
    display: none;
  }

  .container.margin-vert--lg .row .col.col--7 {
    --ifm-col-width: 100% !important;
    padding: 24px 16px !important;
  }

  .main-wrapper {
    .container.margin-vert--lg {
      max-width: 96% !important;
    }
  }
}

/* Pill tabs */

.markdown .tabs.pill-tabs {
  border-bottom: 0;

  .tabs__item {
    margin-right: 10px;
    border-radius: var(--ifm-global-radius);
    border: 2px solid var(--ifm-table-border-color);

    &:hover {
      border-radius: var(--ifm-global-radius);
    }

    &.tabs__item--active {
      border-color: var(--ifm-tabs-color-active);
    }
  }
}

/*
Prevent useless bottom margin for multiline <figcaption> tags in MDX:
<figcaption>
  some paragraph text
</figcaption>
 */
figcaption > p:last-child {
  margin-bottom: 0;
}

/* Diffing with code syntax highlighting */
.code-remove-line {
  background-color: var(--home-code-diff-removed);
  .token {
    background-color: var(--home-code-diff-removed-word);
  }
  display: block;
  margin: 0 calc(-1 * var(--ifm-pre-padding));
  padding: 0 var(--ifm-pre-padding);
}

.code-add-line {
  background-color: var(--home-code-diff-added);
  .token {
    background-color: var(--home-code-diff-added-word);
  }
  display: block;
  margin: 0 calc(-1 * var(--ifm-pre-padding));
  padding: 0 var(--ifm-pre-padding);
}

img {
  &.center {
    width: 100%;
    justify-content: center;
  }
  &.half-size {
    max-height: 50vh;
    object-fit: contain;
  }
}

@media only screen and (max-width: 600px) {
  .tweet {
    display: none;
  }
}
