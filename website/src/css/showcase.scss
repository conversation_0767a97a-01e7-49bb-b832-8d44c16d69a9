/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

@import "shared";

:root {
  --showcase-icon-background: rgb(0 0 0 / 1.5%);
}

html[data-theme="dark"] {
  --showcase-icon-background: rgb(255 255 255 / 1.5%);
}

.plugin-pages .main-wrapper {
  width: 100%;
  max-width: 100%;
}

.sectionContainer {
  max-width: 1360px;
  padding: 0 20px;
  margin: 0 auto;
  position: relative;
}

.headerContainer {
  h1 {
    margin: 16px 0 28px;
    color: white;

    span {
      color: var(--ifm-color-primary);
    }
  }

  p {
    color: var(--ifm-color-secondary-dark);
    font-size: 19px;
  }
}

.showcaseSection {
  max-width: 1320px;
  text-align: center;
  margin: 8px auto 54px;
  padding: 20px 16px 16px;
  border: 1px solid var(--ifm-hr-border-color);
  border-radius: 12px;

  &.showcaseCustomers {
    border: 0;
    box-shadow: none;
  }

  &:first-child {
    margin-top: 12px;
  }

  &:last-child {
    margin-bottom: 0;
  }

  h2 {
    margin: 4px -20px 12px;
    padding: 0 40px 0;
    text-align: start;

    img {
      margin: 16px 0 8px;
      height: 40px;
    }

    &.withLogo {
      min-height: 60px;
    }
  }

  .showcaseSectionDescription {
    text-align: start;
    color: var(--subtle);
    padding: 0 20px;
    margin-bottom: 8px;
  }

  .showcase img {
    height: 80px;
    border-radius: 20px;
  }

  .logos {
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 28px;
    margin: 20px 20px 16px;

    .icon {
      padding: 0;
    }
  }

  .showcase {
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--ifm-hr-border-color);
    transition: transform 0.25s ease-in-out;
    overflow: hidden;
    position: relative;

    h3 {
      margin-bottom: 0;
      line-height: 21px;
      padding: 0 6px 4px;
      font-size: 18px;
      min-height: 2.75rem;
    }

    p {
      margin: 8px 0 18px;
      padding-top: 0 !important;

      a {
        @extend %link-style;
      }
    }

    h3,
    p {
      color: var(--ifm-color-emphasis-800);
    }

    &:hover .icon {
      transform: scale3d(1.03, 1.03, 1);
    }

    .iconBox {
      display: flex;
      justify-content: center;
      background: linear-gradient(var(--showcase-icon-background), transparent);
      padding: 20px 20px 8px;
      width: 100%;
      overflow: visible;
    }

    .iconBackground {
      filter: blur(28px);
      position: absolute;
      opacity: 0.16;
      transform: scale(1.38);
    }

    .icon {
      box-shadow: 0 8px 16px rgb(20 20 20 / 7%);
      position: relative;
      z-index: 1;
      transition: transform 200ms ease-in-out;
    }

    .showcaseContent {
      padding: 8px 16px 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex-grow: 1;
    }

    .showcaseLinks span {
      color: var(--subtle);
    }
  }

  .articleButton {
    color: var(--ifm-color-emphasis-600);
    font-weight: 500;
    font-size: 15px;
    padding: 6px 12px;
    margin: 0 -17px;
    border-radius: 0;
    border: 0;
    border-top: 1px solid var(--ifm-color-emphasis-300);

    &:hover {
      background: var(--ifm-menu-color-background-hover);
      color: var(--ifm-font-color-secondary);
    }
  }
}

.footerContainer {
  text-align: center;

  p {
    color: #858a96;

    a {
      @extend %link-style-dark;
      color: #fff;
      background: none !important;

      &:hover {
        background: none !important;
        color: var(--ifm-color-primary);
      }
    }
  }

  .formButton {
    @extend %button-link-style;
    color: #fff;
    border-color: var(--ifm-color-primary);
    margin-top: 20px;
    margin-bottom: 36px;
    border-width: 2px;
    font-weight: 500;

    &:hover {
      color: var(--ifm-color-primary);
    }
  }
}

html[data-theme="dark"] .showcaseSection {
  p a {
    @extend %link-style-dark;
  }

  .articleButton {
    color: var(--ifm-color-emphasis-400);
  }

  .formButton {
    color: var(--ifm-color-secondary-dark);
  }

  .iconBackground {
    opacity: 0.15;
  }
}

.home-showcase-section {
  max-width: 800px;
  margin: 20px auto;
  text-align: center;
  padding-bottom: 40px;

  p {
    max-width: 560px;
    margin: 0 auto;
  }
}

.pinned img {
  width: 150px;
  border-radius: 20px;
}

.footnote {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
}

.home-showcase-section {
  h2 {
    font-size: 31px;
    line-height: 40px;
    margin: 10px 0;
  }

  .showcase img {
    width: 100px;
    height: 100px;
    border-radius: 20px;
  }
}

.showcaseHeader {
  padding-bottom: 15px;
  padding-top: 15px;
  text-align: center;
}

@media only screen and (min-width: 600px) {
  .showcaseSection .logos {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media only screen and (min-width: 768px) {
  .showcaseSection .logos {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media only screen and (min-width: 996px) {
  .showcaseSection .logos {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media only screen and (min-width: 1320px) {
  .showcaseSection .logos {
    grid-template-columns: repeat(5, 1fr);
  }
}

@media only screen and (max-width: 1400px) {
  .showcaseSection {
    margin-left: 20px;
    margin-right: 20px;
  }
}
