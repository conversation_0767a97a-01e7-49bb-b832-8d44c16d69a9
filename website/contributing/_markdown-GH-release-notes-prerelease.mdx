```markdown
- <!-- TO<PERSON><PERSON> List out notable picks for this patch -->

---

To test it, run:

<!-- TODO Update with your version -->

npx react-native init RN069RC0 --version 0.69.0-rc.0

---

- You can participate in the conversation on the status of this release in the [working group](https://github.com/reactwg/react-native-releases/discussions).

- To help you upgrade to this version, you can use the [upgrade helper](https://react-native-community.github.io/upgrade-helper/) ⚛️

- See changes from this release in the [changelog PR](https://github.com/facebook/react-native/labels/%F0%9F%93%9D%20Changelog)

---

### Help us testing 🧪

<!-- TODO Add the call to action for something specific that we want folks to test -->

Let us know how it went by posting a comment in the [working group discussion](https://github.com/reactwg/react-native-releases/discussions)! Please specify with system you tried it on (ex. macos, windows).

**Bonus points:** It would be even better if you could swap things around: instead of using a fresh new app, use a more complex one - or use a different library that is already leveraging the new architecture!
```

<figure>
  <img
    width="400"
    alt="Creating a GitHub Release"
    src="https://user-images.githubusercontent.com/1309636/133348648-c33f82b8-b8d2-474a-a06e-35a1fb8d18de.png"
  />
  <figcaption>Creating a GitHub Release.</figcaption>
</figure>
