---
title: Contribution License Agreement
---

You must sign a Contribution License Agreement (CLA) before your pull request can be merged. This a one-time requirement for Meta projects in GitHub. You can read more about [Contributor License Agreements (CLA)](https://en.wikipedia.org/wiki/Contributor_License_Agreement) on Wikipedia.

However, you don't have to do this up-front. We welcome you to follow, fork, and submit a pull request.

When your pull request is created, it is classified by the Facebook GitHub bot. If you have not signed a CLA, the bot will provide instructions for [signing a CLA](https://code.facebook.com/cla) before your pull request can be considered eligible for merging. Once you have done so, the current and all future pull requests will be labelled as "CLA Signed".

<!--alex ignore simple-->

Signing the CLA might sound scary, but it's actually very simple and can be done in less than a minute.
