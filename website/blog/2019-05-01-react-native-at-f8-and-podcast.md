---
title: React Native at F8 and Open Source Podcast
authors: [cpojer]
tags: [announcement]
---

This week, [<PERSON>](https://twitter.com/<PERSON>_<PERSON>) gave a talk at [F8 2019](https://developers.facebook.com/videos/2019/mobile-innovation-with-react-native-componentkit-and-litho/) about React Native in Facebook's Android and iOS applications. We are excited to share what we've been up to for the past two years and what we're doing next.

Check out the video on [Facebook's developer website](https://developers.facebook.com/videos/2019/mobile-innovation-with-react-native-componentkit-and-litho/):

<a href="https://developers.facebook.com/videos/2019/mobile-innovation-with-react-native-componentkit-and-litho/">
  <img
    src="/blog/assets/eli-at-f8.png"
    alt="F8 Talk about React Native"
  />
</a>

#### Highlights from the talk:

- We spent 2017 and 2018 focused on React Native's largest product, Facebook's Marketplace. We collaborated with the Marketplace team to improve quality and add delight to the product. At this point, Marketplace is one of the highest quality products in the Facebook app both on Android and iOS.
- Marketplace's performance was a big challenge as well, especially on mid-end Android devices. We cut startup time by more than 50% over the last year with more improvements on the way! The biggest improvements are being built into React Native and will be coming to the community later this year.
- We have the confidence that we can build the high quality and performant apps that Facebook needs with React Native. This confidence has let us invest in bigger bets, like [rethinking the core of React Native](https://www.youtube.com/watch?v=UcqRXTriUVI&app=desktop).
- Microsoft supports and uses React Native for Windows, enabling people to use their expertise and codebase to render to Microsofts's Universal Windows Platform. Check out Microsoft Build next week to [hear them talk about that more](https://mybuild.techcommunity.microsoft.com/sessions/77321).

### React Radio Podcast about Open Source

Eli's talk concludes by talking about our recent open source work. We gave [an update on our progress in March](/blog/2019/03/01/react-native-open-source-update) and recently [Nader Dabit](https://twitter.com/dabit3) and [Gant Laborde](https://twitter.com/GantLaborde) invited Christoph for a chat on their podcast, [React Native Radio](https://devchat.tv/react-native-radio/react-native-open-source-the-react-native-community-feat-christoph-nakazawa/), to chat about React Native in open source.

#### Highlights from the podcast:

- We talked about how the React Native team at Facebook thinks about open source and how we are building a sustainable community that scales for a project of React Native's [size](https://octoverse.github.com/projects#repositories).
- We are on track to remove multiple modules as part of the [Lean Core](https://github.com/facebook/react-native/issues/23313) effort. Many modules like WebView and the React Native CLI have received more than 100 Pull Requests since they were extracted.
- Next, we'll be focusing on overhauling the React Native website and documentation. Stay tuned!

You'll find the episode in your favorite podcasting app soon or you can listen to the recording right here:

<audio controls style={{display: 'block', margin: '0 auto'}} src="https://media.devchat.tv/reactnativeradio/React_Native_Radio_Episode_121.mp3"> {' '} Audio is unsupported in this browser.{' '} </audio>
