---
title: <PERSON>act Native Is Hiring Managers, To Expand Beyond Mobile
authors: [<PERSON>]
tags: [hiring]
---

We recently shared [React Native’s Many Platform Vision](https://reactnative.dev/blog/2021/08/26/many-platform-vision) for how expanding React to other platforms improves the framework for everyone else. We’ve been making significant progress on this vision over the last couple years by partnering with Microsoft on React Native for Windows and macOS, and Oculus on React Native in VR.

As [part of our plans beginning earlier this year](https://reactnative.dev/blog/2021/08/19/h2-2021), we are growing our focus on these platforms and growing our teams to help us achieve our vision. In order to support our new teammates, and many more to come, **we are hiring two Engineering Managers: one to help support React Native for Desktop, and one to support React Native for VR**.

<!--truncate-->

<figure>
  <img src="/blog/assets/many-platform-vision-messenger-desktop.png" alt="Screenshot of the Messenger app on macOS" />
  <figcaption>
    React Native powers Video Calling in Messenger for Windows and macOS.
  </figcaption>
</figure>

<figure>
  <img src="/blog/assets/many-platform-vision-oculus-home.png" alt="Screenshot of Oculus Home in virtual reality" />
  <figcaption>
    React and Relay power the Oculus Home and many other virtual reality experiences.
  </figcaption>
</figure>

We are looking for managers who will care deeply about the growth and success of our engineers, and are excited about our vision. Previous React or React Native experience is not required. This role is open to anyone in the United States. If this sounds like an interesting opportunity and you are interested in learning more, please apply on [Facebook’s Career’s Page](https://www.facebook.com/careers/v2/jobs/438516437547870). We look forward to hearing from you!
