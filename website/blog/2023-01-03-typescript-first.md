---
title: First-class Support for TypeScript
authors: [l<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>]
tags: [typescript, engineering]
date: 2023-01-03
---

# First-class Support for TypeScript

With the release of 0.71, React Native is investing in the TypeScript experience with the following changes:

- [New app template is TypeScript by default](/blog/2023/01/03/typescript-first#new-app-template-is-typescript-by-default)
- [TypeScript declarations shipped with React Native](/blog/2023/01/03/typescript-first#declarations-shipped-with-react-native)
- [React Native documentation is TypeScript First](/blog/2023/01/03/typescript-first#documentation-is-typescript-first)

In this post we’ll cover what these changes mean for you as a TypeScript or Flow user.

<!--truncate-->

## New App Template is TypeScript By Default

Starting with 0.71, when you create a new React Native app via the React Native CLI you'll get a TypeScript app by default!

```shell
npx react-native init My71App --version 0.71.0
```

![Screenshot of an iPhone simulator running a new app generated by React Native CLI. Alongside the simulator is a screenshot of Visual Studio Code editor opened to "App.tsx" to illustrate it is running a TypeScript file.](/blog/assets/typescript-first-new-app.png)

The starting point of a newly generated app will be `App.tsx` instead of `App.js` – fully TypeScript typed. The new project is already set up with a `tsconfig.json` so out of the box your IDE will help you write typed code right away!

## Declarations Shipped with React Native

0.71 is the first release with built-in TypeScript (TS) declarations.

Prior, TypeScript declarations for React Native were provided by [`@types/react-native`](https://www.npmjs.com/package/@types/react-native) hosted in the [DefinitelyTyped](https://github.com/DefinitelyTyped/DefinitelyTyped) repository. The decision to co-locate TypeScript types with React Native source was for improved correctness and maintenance.

`@types/react-native` only provides types for stable releases. This means if you ever wanted to develop with a pre-release version of React Native in TypeScript, you’d have to use types from an older release which may be inaccurate. Releasing `@types/react-native` is also error-prone. The releases lag React Native releases, and the process involves manually inspecting for type changes made to React Native’s public API and updating the TS declaration to match.

With TS types co-located with React Native source, there is more visibility and ownership of TS types. Our team is actively working on tools to maintain alignment between Flow and TS.

This change also removes a dependency for React Native users to manage. When upgrading to 0.71 or above, you can remove `@types/react-native` as a dependency. [Refer to the new app template on how to set up TypeScript support.](https://github.com/facebook/react-native/blob/main/template/tsconfig.json)

We plan to deprecate `@types/react-native` for versions 0.73 and onward. Concretely this means:

- `@types/react-native` tracking React Native versions 0.71 and 0.72 will be released. They will be identical to the types in React Native on the relevant release branches.
- For React Native 0.73 and onward, TS types will only be available from React Native.

### How to Migrate

Please migrate to the new co-located types at your earliest convenience. Here are more details on migrating based on your needs.

#### App Maintainer

Once you upgrade to React Native >= 0.71, you can remove the `@types/react-native` from your `devDependency`.

:::note

If you have warnings because a library you use references `@types/react-native` as a `peerDependency`, file an issue or open a PR for that library to use [optional peerDependencies](https://docs.npmjs.com/cli/v7/configuring-npm/package-json#peerdependenciesmeta) and ignore the warning for now.

:::

#### Library Maintainer

Libraries that target versions of React Native below 0.71 may use a `peerDependency` of `@types/react-native` to typecheck against the apps version of typings. This dependency should be marked as optional in [`peerDependenciesMeta`](https://docs.npmjs.com/cli/v7/configuring-npm/package-json#peerdependenciesmeta) so that the typings are not required for users without TypeScript or for 0.71 users where typings are built-in.

#### Maintainer of TypeScript declarations that depend on `@types/react-native`

Check out the [breaking changes introduced with 0.71](https://github.com/facebook/react-native/blob/main/CHANGELOG.md) to see if you’re ready to migrate.

### What if I use Flow?

Flow users can continue to typecheck applications targeting 0.71+ but configuration logic for it is no longer included out-of-the box in the template.

Flow users have previously upgraded React Native’s Flow types by merging in the `.flowconfig` from the new app template and manually updating `flow-bin`. The new app template no longer has a `.flowconfig`, but [one is still present in the React Native repository](https://github.com/facebook/react-native/blob/main/.flowconfig) that can be used as a basis for your app.

If you need to start a new React Native app in Flow, you can reference the [new app template from 0.70](https://github.com/facebook/react-native/tree/0.70-stable/template).

### What if I find a bug in the TypeScript declaration?

Regardless of whether you’re using built-in TS types or `@types/react-native`, if you find a bug please submit a PR to both [React Native](https://github.com/facebook/react-native) and [DefinitelyTyped](https://github.com/DefinitelyTyped/DefinitelyTyped) repositories. If you don’t know how to fix it, please file a GitHub issue in the React Native repository and mention [@lunaleaps](https://github.com/lunaleaps) on the issue.

## Documentation is TypeScript First

To ensure a consistent TypeScript experience, we have made several updates to the React Native documentation to reflect TypeScript as the new default language.

Code examples now allow inline TypeScript and over 170 interactive code examples have been updated to pass linting, formatting, and type-checking in the new template. Most examples are valid as both TypeScript and JavaScript. Where they are incompatible, you can view the example in either language.

If you spot a mistake or you have an improvement, remember that the website is also open source and we would love to see your PRs!

## Thank you to the React Native TypeScript community!

In closing, we want to recognize all the work done over the years by the community to ensure that TypeScript is usable by React Native developers.

We want to thank all the [contributors](https://github.com/DefinitelyTyped/DefinitelyTyped/blob/master/types/react-native/index.d.ts#L3) that have been maintaining `@types/react-native` since [2015](https://github.com/DefinitelyTyped/DefinitelyTyped/commit/efce0c25ec532a4651859f10eda49e97a5716a42)! We see the effort and care you all have put into making sure React Native users have the best experience.

Thank you to [@acoates](https://github.com/acoates), [@eps1lon](https://github.com/eps1lon), [@kelset](https://github.com/kelset), [@tido64](https://github.com/tido64), [@Titozzz](https://github.com/Titozzz), and [@ZihanChen-MSFT](https://github.com/ZihanChen-MSFT) for your help consulting, questioning, communicating and reviewing changes to move the TypeScript types to React Native.

Similarly, we want to thank the [maintainers of `react-native-template-typescript`](https://github.com/react-native-community/react-native-template-typescript/graphs/contributors) for supporting the TypeScript experience for new app development in React Native since day one.

We look forward to collaborating more directly in the React Native repository and continue improving the React Native developer experience!
