---
title: 'React Native: A year in review'
author: <PERSON>
authorTitle: Software Engineer at Facebook
authorURL: 'https://github.com/mkonicek'
authorImageURL: 'https://avatars1.githubusercontent.com/u/346214?v=3&s=460'
authorTwitter: martinkonicek
hero: '/blog/assets/big-hero.png'
tags: [announcement]
---

It's been one year since we open-sourced React Native. What started as an idea with a handful of engineers is now a framework being used by product teams across Facebook and beyond. Today at F8 we announced that Microsoft is bringing [React Native to the Windows ecosystem](https://microsoft.github.io/code-push/articles/ReactNativeWindows.html), giving developers the potential to build React Native on Windows PC, Phone, and Xbox. It will also provide open source tools and services such as a React Native extension for Visual Studio Code and CodePush to help developers create React Native apps on the Windows platform. In addition, [Samsung](https://www.tizen.org/blogs) is building React Native for its hybrid platform, which will empower developers to build apps for millions of SmartTVs and mobile and wearable devices. We also released the [Facebook SDK for React Native](https://github.com/facebook/react-native-fbsdk), which makes it easier for developers to incorporate Facebook social features like Login, Sharing, App Analytics, and Graph APIs into their apps. In one year, React Native has changed the way developers build on every major platform.

It's been an epic ride — but we are only getting started. Here is a look back at how React Native has grown and evolved since we open-sourced it a year ago, some challenges we faced along the way, and what we expect as we look ahead to the future.

<footer>
  <a
    href="https://code.facebook.com/posts/597378980427792/react-native-a-year-in-review/"
    className="btn">Read more</a>
</footer>

> This is an excerpt. Read the rest of the post on [Facebook Code](https://code.facebook.com/posts/597378980427792/react-native-a-year-in-review/).
