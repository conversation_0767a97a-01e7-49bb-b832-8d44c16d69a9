---
title: 'React Native for Android: How we built the first cross-platform React Native app'
author: <PERSON>
authorTitle: Software Engineer at Facebook
authorURL: 'https://www.facebook.com/drwitte'
authorFBID: 210064
hero: '/blog/assets/blue-hero.png'
tags: [announcement]
---

Earlier this year, we introduced [React Native for iOS](https://code.facebook.com/posts/1014532261909640/react-native-bringing-modern-web-techniques-to-mobile/). React Native brings what developers are used to from React on the web — declarative self-contained UI components and fast development cycles — to the mobile platform, while retaining the speed, fidelity, and feel of native applications. Today, we're happy to release React Native for Android.

At Facebook we've been using React Native in production for over a year now. Almost exactly a year ago, our team set out to develop the [Ads Manager app](https://www.facebook.com/business/news/ads-manager-app). Our goal was to create a new app to let the millions of people who advertise on Facebook manage their accounts and create new ads on the go. It ended up being not only Facebook's first fully React Native app but also the first cross-platform one. In this post, we'd like to share with you how we built this app, how React Native enabled us to move faster, and the lessons we learned.

<footer>
  <a
    href="https://code.facebook.com/posts/****************/react-native-for-android-how-we-built-the-first-cross-platform-react-native-app/"
    className="btn">Read more</a>
</footer>

> This is an excerpt. Read the rest of the post on [Facebook Code](https://code.facebook.com/posts/****************/react-native-for-android-how-we-built-the-first-cross-platform-react-native-app/).
