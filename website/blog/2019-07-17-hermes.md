---
title: <PERSON>, a new JavaScript Engine optimized for React Native
author: <PERSON>
authorTitle: Documentation Engineer at Facebook
authorURL: 'https://twitter.com/rachelnabors'
authorImageURL: 'https://avatars0.githubusercontent.com/u/236306?s=460&v=4'
authorTwitter: rache<PERSON><PERSON><PERSON>
tags: [announcement]
---

Last week at Chain React we announced Hermes, an open source JavaScript engine we’ve been working on at Facebook. It’s a small and lightweight JavaScript engine optimized for running React Native on Android. [Check it out!](https://code.fb.com/android/hermes/)

Her<PERSON> improves React Native performance by decreasing memory utilization, reducing download size, and decreasing the time it takes for the app to become usable or “time to interactive” (TTI).

> “As we analyzed performance data, we noticed that the JavaScript engine itself was a significant factor in startup performance and download size. With this data in hand, we knew we had to optimize JavaScript performance in the more constrained environments of a mobile phone compared with a desktop or laptop. After exploring other options, we built a new JavaScript engine we call Hermes. It is designed to improve app performance, focusing on our React Native apps, even on mass-market devices with limited memory, slow storage, and reduced computing power.” —[Hermes: An open source JavaScript engine optimized for mobile apps, starting with React Native](https://code.fb.com/android/hermes/)

Want to get started right away? Be sure to [check out our new guide to enabling Hermes in your existing React Native app](/docs/hermes/) in the docs!

[![Illustration of the Hermes and React Native logos joined into a winged fury, rising in a crashing electrical storm from a lone, glowing, presumably Android phone.](/blog/assets/2019_hermes-launch-illo-rachel-nabors.jpg)](https://code.fb.com/android/hermes/) _Illustration by Rachel Nabors_
