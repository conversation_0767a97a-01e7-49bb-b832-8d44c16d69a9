---
title: React Native Accessibility - GAAD 2022 Update
author: <PERSON>
authorTitle: React Native Accessibility Community Manager
authorURL: 'https://twitter.com/AT_Fresh_Dev'
authorImageURL: 'https://github.com/alextait1.png'
authorTwitter: AT_Fresh_Dev
tags: [announcement]
date: 2022-05-19
---

May 19th, 2022 marks the 11th annual celebration of Global Accessibility Awareness Day and we wanted to update everyone on the accessibility progress we’ve made on the React Native Framework. Meta (formerly Facebook) was the [first organization to take the GAAD pledge in 2020](/blog/2021/03/08/GAAD-React-Native-Accessibility), committing to making the React Native framework accessible.

> _“We hope this pledge makes it easier for developers using React Native to create fully accessible mobile apps and inspires other organizations to make similar commitments to a more accessible future.”_
>
> — [<PERSON>, head of accessibility Meta, 2020](https://gaad.foundation/gaadpledge/)

The process initially began with a thorough review and gap analysis of the framework focused on React Native utilized the iOS and Android APIs to support accessibility features. Dozens of issues have since been fixed or closed out, making good on the pledge to make React Native accessible and advancing the accessibility of the framework ever forward.

We didn’t stop there, and in early 2022, we reviewed and prioritized the remaining issues from this gap analysis on the [Improved React Native Accessibility Board](https://github.com/facebook/react-native/projects/15) based on their impact on developers and end users.

<!--truncate-->

## Fixes that have already landed in 2022

- [Android: Disabled state not announced/disabled functionality not applied for some components](https://github.com/facebook/react-native/issues/30840)
- [Android: Position in collection not supported by some components](https://github.com/facebook/react-native/issues/30977)
- [Android: Make links independently focusable by Talkback](https://github.com/facebook/react-native/pull/31757)

## Fixes currently in progress

- [iOS/Android: Text input error announcement](https://github.com/facebook/react-native/issues/30848)
- [Android: Form field error state announcement](https://github.com/facebook/react-native/issues/30859)
- [Android: Role description is announced before the components text, rather than after](https://github.com/facebook/react-native/issues/31042)

## Contributor highlight

<!-- alex disable he-she herself-himself her-him -->

We want to recognize and send a tremendous thank you to [Fabrizo Bertoglio](https://github.com/fabriziobertoglio1987) who has contributed several high-quality accessibility fixes to React Native in 2021 and 2022.

Fabrizio has made it a personal goal to enable users through high quality solutions. By learning how to use TalkBack and VoiceOver himself, he has discovered how difficult it can be for screen reader users to experience everyday applications. He wants to build software that removes friction from these experiences and please disabled users.

His pull requests have been very high quality, well documented, and include thorough test cases. Well done Fabrizio! The React Native Accessibility community thanks you for your many outstanding contributions.

<!-- alex enable he-she herself-himself her-him -->

## What’s next in 2022

Our goal is to fix as many of the remaining accessibility issues as possible in 2022. We will also be reviewing the backlog of [React Native community reported issues](https://github.com/facebook/react-native/issues) to look for any new accessibility-related requests.
