---
title: The State of the React Native Community in 2018
author: <PERSON>
authorTitle: Core Maintainer & React Native Developer
authorURL: 'https://github.com/kelset'
authorImageURL: 'https://avatars2.githubusercontent.com/u/16104054?s=460&v=4'
authorTwitter: kelset
tags: [announcement]
---

In 2018 the React Native Community made a number of changes to the way we develop and communicate about React Native. We believe that a few years from now we will look back and see that this shift was a turning point for React Native.

A lot of people are excited about the rewrite of React Native's architecture, widely known as [Fabric](https://github.com/react-native-community/discussions-and-proposals/issues/4). Among other things, this will fix fundamental limitations in React Native's architecture and will set up React Native for success in the future together with [JSI and TurboModules](https://github.com/react-native-community/discussions-and-proposals/issues/40).

The biggest shift in 2018 was to empower the React Native Community. From the beginning, Facebook encouraged developers from all around the world to participate in React Native's open source project. Since then, a number of core contributors emerged to handle, among other things, the release process.

These members took a few substantial steps towards making the whole community more empowered to shape the future of this project with the following resources:

## [`react-native-releases`](https://github.com/react-native-community/react-native-releases) 📬

This repository, created in January, serves the dual purpose of allowing everyone to keep up the new releases in a more collaborative manner and opened the conversation of what would be part of a certain release to whomever wanted to suggest a cherry-pick (like for [0.57.8](https://github.com/react-native-community/react-native-releases/issues/71) and all its previous versions).

This has been the driving force behind moving away from a monthly release cycle, and the "long term support" approach currently used for version 0.57.x.

Half of the credit for reaching these decisions goes to the other repository created this year:

## [`discussions-and-proposals`](https://github.com/react-native-community/discussions-and-proposals) 🗣

This repository, created in July, expanded on the idea of a more open environment for conversations on React Native. Previously, this need was handled by issues labelled [`For Discussion`](https://github.com/facebook/react-native/labels/For%20Discussion) in the main repository, but we wanted to expand this strategy to an RFC approach that other libraries have (e.g. React).

This experiment immediately found its role in the React Native lifecycle. The Facebook team is now using the community RFC process to discuss what could [be improved in React Native](https://github.com/react-native-community/discussions-and-proposals/issues/64), and coordinate the efforts around the [Lean Core project](https://github.com/react-native-community/discussions-and-proposals/issues/6) - among other interesting discussions.

## [@ReactNativeComm](https://twitter.com/ReactNativeComm) 🐣

We are aware that our approach to communicate these efforts has not been as effective as we would have liked, and in an attempt to give you all an easier time keeping up with everything going on in the React Native Community (from releases to active discussions) we created a new twitter account that you can rely on [@ReactNativeComm](https://twitter.com/ReactNativeComm).

If you are not on that social network, remember that you can always watch repositories via GitHub; this feature improved these past few months with the possibility of being notified only for releases, so you should consider using it anyway.

## What awaits ahead 🎓

Over the past 7-8 months, core contributors enhanced the [React Native Community GitHub organization](https://github.com/react-native-community) to take more ownership over the development of React Native, and enhance collaboration with Facebook. But this always lacked the formal structure that similar projects may have in place.

This organization can set the example for everyone in the larger developer community by enforcing a set of standards for all the packages/repos hosted in it, providing a single place for maintainers to help each other and contribute quality code that conforms to community-agreed standards.

In early 2019, we will have this new set of guidelines in place. Let us know what you think in the [dedicated discussion](https://github.com/react-native-community/discussions-and-proposals/issues/63).

We are confident that with these changes, the community will become more collaborative so that when we reach 1.0, we will all continue to write (even more) awesome apps by leveraging this joint effort 🤗

---

I hope you are as excited as we are about the future of this community. We're excited to see all of you involved either in the conversations happening in the repositories listed above or via the awesome code you’ll produce.

Happy coding!
