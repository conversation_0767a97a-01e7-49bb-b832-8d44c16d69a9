---
title: React Native Documentation Update
author: <PERSON>
authorTitle: Documentation Engineer at Facebook
authorURL: 'https://twitter.com/rachelnabors'
authorImageURL: 'https://avatars0.githubusercontent.com/u/236306?s=460&v=4'
authorTwitter: rachelnab<PERSON>
tags: [announcement]
---

Last year we conducted user interviews and sent out [a survey](https://www.surveymonkey.co.uk/r/DDZWQDJ) to learn more about how and when people use the React Native docs. With the data and guidance gleaned from 24 interviews and over 3000 survey responses, we've been able to work to improve React Native's documentation, and we're excited to share that progress today:

- **[New Getting Started guides](https://reactnative.dev/docs/getting-started)** We launched new Getting Started with docs to [explain what Native Components](https://reactnative.dev/docs/intro-react-native-components) are to people with no mobile development background. We also included a [refresher/introduction to React](https://reactnative.dev/docs/intro-react) to help folks getting started with React for the first time!
- **[New Testing guide](https://reactnative.dev/docs/testing-overview)** We worked with <PERSON><PERSON><PERSON><PERSON> to create a new illustrated testing guide that introduces app developers to different kinds of testing strategies and how they work in a React Native workflow.
- **[New Security guide](https://reactnative.dev/docs/security)** We worked with Kadi Kraman to create a new illustrated security guide that explains the basics of security in a React Native world and puts forth best in class solutions.
- **More illustrations** We've added fancy new illustrations, including the new [`Pressable`](https://reactnative.dev/docs/pressable) and [introduction to React Native components](https://reactnative.dev/docs/intro-react-native-components) docs
- **https://reactnative.dev** After 5 years we finally moved to our own domain! **reactnative.dev** is easier to autocomplete from your browser bar and is easier to type out than our previous **github.io** address!
- **Site design and architecture improvements** We've updated the design and site architecture to surface and categorize more of our guides and make content in the API reference more readable. Kudos especially to [Bartosz Kaszubowski](https://github.com/Simek) whose attention to detail and collaboration has made many of these changes possible quickly!
- **Updated Core Component and API docs** We held a [documentation drive](https://github.com/facebook/react-native-website/issues/1579) to update reference docs! Thanks to these and other participants we were able to fully update the docs and add Snack examples to all of them in time for 0.62: [Marta Dabrowka](https://twitter.com/martadabrowka), [Abraham Nnaji](https://twitter.com/nnajiabraham), [Ahmed Talaat El-Hager](https://twitter.com/ahmdtalat), [Mohamed Abdel Nasser Abdou](https://twitter.com/mohamedsgap), [Danilo Britto](https://twitter.com/danilobrinu), [Mitul Savani](https://twitter.com/mitulsavani), [Kaio Duarte](https://twitter.com/kaiodduarte), [Pablo Espinosa](https://twitter.com/espipj), [Jesse Katsumata](https://twitter.com/natural_clar), [I Gede Agastya Darma Laksana](https://twitter.com/gedeagas), [Sebastião Bruno Kiafuka Fernando](https://twitter.com/bruno_kiafuka), [Miguel Bolivar](https://twitter.com/Darking360), [Dani Akash](https://twitter.com/dani_akash_), [Luiz Celso de Faria Alves](https://twitter.com/_eucelso), and [Bartosz Kaszubowski](https://twitter.com/simek). With their contributions, these are the best and most up to date React Native docs yet!
- **Keep those PRs coming!** We are able to consistently keep our open PRs under 10 per week! Thank you for sending them!

Thank you so much to everyone who participated in the interviews, the survey, and our documentation efforts! Your collaboration makes this possible.

<!--truncate-->

## What’s next?

The global COVID-19 pandemic has impacted many community members’ jobs.

We are responding with additional content including:

- New and improved Native Modules guides
- Introductory content for people coming in to React Native for the first time

### You can help!

There are many ways you can help us write even better docs!

1. If you see a typo, run into an issue with a guide, or something otherwise isn’t quite right, click that “Edit” button and submit a PR.
2. [Participate in our survey](https://www.surveymonkey.co.uk/r/DDZWQDJ)—this helps us understand how you use React Native and its documentation
3. Write for us! We’re working on a tutorial section as well as guides for topics like offline apps, navigation, accessibility, debugging, animations, internationalization, and performance. If you or someone you admire or know is a perfect fit for any of these, please [reach out to me](https://twitter.com/rachelnabors)!
