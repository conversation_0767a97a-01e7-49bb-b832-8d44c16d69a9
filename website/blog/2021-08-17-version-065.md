---
title: Announcing React Native 0.65
authors: [lunaleaps]
tags: [announcement, release]
---

Today we’re releasing React Native version 0.65 with a new version of <PERSON><PERSON>, improvements to accessibility, package upgrades, and more.

## What's new in Hermes 0.8?

[Her<PERSON>](https://hermesengine.dev), Facebook’s open source JavaScript VM optimized for React Native, has been upgraded to version 0.8.1. Some of the stand-out features in this release are:

- A new concurrent garbage collector titled “Hades” which delivers up to 30 times shorter pause times on 64 bit devices. At Facebook, we saw this improve some CPU-intensive workloads by 20%-50%. You can [learn more about Hades here](https://hermesengine.dev/docs/hades/).
- [ECMAScript Internationalization API (ECMA-402, or `Intl`)](https://hermesengine.dev/docs/intl) is now built into Hermes on Android and enabled by default, with only 57-62K per API size overhead (compared to [JSC's 6MiB](https://github.com/react-native-community/jsc-android-buildscripts)). With this change, Hermes users no longer require locale polyfills. A big thank you to [@mganandraj](https://github.com/mganandraj) and other partners at Microsoft for driving the implementation to make this happen!
- [Hermes on iOS](/blog/2021/03/12/version-0.64) now [supports Apple M1 Macs and Mac Catalyst](https://github.com/facebook/hermes/pull/546)!
- Memory improvements including SMI (Small Integers) and pointer compression that [shrank JS heap by 30%](https://twitter.com/tmikov/status/1385629737121243140).
- Changes to `Function.prototype.toString` that [fixed a performance drop due to improper feature detection](https://github.com/facebook/hermes/issues/471#issuecomment-820123463) and [supports the source code injecting use case](https://github.com/facebook/hermes/issues/114).

You can find the full [Hermes changelog here](https://github.com/facebook/hermes/releases).

[Follow steps here](/docs/hermes#enabling-hermes) to opt-in your app to Hermes if you haven’t already to leverage these new features and gains!

## Accessibility Fixes and Additions

Last year [Facebook took the GAAD pledge](https://reactnative.dev/blog/2021/05/20/GAAD-One-Year-Later) to improve accessibility within React Native. 0.65 shares the results of this pledge and other accessibility wins! Some notable changes include:

- Allow specification of high contrast light and dark values for iOS. See [documentation](/docs/dynamiccolorios) for more details.
- Added [`getRecommendedTimeoutMillis`](/docs/accessibilityinfo#getrecommendedtimeoutmillis-android) API on Android. This exposes a user’s preferred default timeout value as set in Android’s accessibility options and is for users who may need extra time to review or reach controls, etc.
- General fixes to ensure TalkBack/VoiceOver properly announce UI states such as `disabled` and `unselected` on components.

You can follow along or contribute to our [outstanding accessibility issues](https://github.com/facebook/react-native/projects/15) here!

## Notable Dependency Version Updates and Gotchas

- `react-native-codegen` version `0.0.7` is now needed as a `devDependency` in the `package.json`.
- JCenter has been sunsetted and read-only now. We have removed JCenter as a maven repository and updated dependencies to use MavenCentral and Jitpack.
- Upgraded OkHttp from v3 to v4.9.1. See [Upgrading to OkHttp 4](https://square.github.io/okhttp/upgrading_to_okhttp_4/) for more details on changes.
- Upgraded to Flipper 0.93 to support Xcode 12.5. See [Flipper changelog here](https://github.com/facebook/flipper/blob/master/desktop/static/CHANGELOG.md).
- Android Gradle Plugin 7 support
- Apple Silicon requires a linker workaround. See [@mikehardy’s note](https://github.com/react-native-community/releases/issues/238#issuecomment-890367992) about this.

## Thank You!

This release includes over **1100 commits** from **61 contributors**. Thank you to everyone who has contributed and supported this release! You can find the [full changelog here](https://github.com/facebook/react-native/blob/main/CHANGELOG.md#v0650).
