---
title: Announcing React Native 0.70
authors: [d<PERSON><PERSON><PERSON><PERSON>n, titozzz, cortinico, kelset]
tags: [announcement, release]
---

# Announcing 0.70

We are excited to release a new version of React Native, 0.70.0. This version comes with several improvements like a new unified configuration for Codegen, <PERSON><PERSON> as default engine, and full CMake support for Android builds along with a refresh of the documentation for the New Architecture. Read on to learn more!

### Sections

- [New Architecture’s New Documentation](/blog/2022/09/05/version-070#new-architectures-new-documentation)
- [Hermes as default engine](/blog/2022/09/05/version-070#hermes-as-default-engine)
- [A new unified configuration for Codegen](/blog/2022/09/05/version-070#a-new-unified-configuration-for-codegen)
- [Android Auto-linking for New Architecture libraries](/blog/2022/09/05/version-070#android-auto-linking-for-new-architecture-libraries)
- [Full CMake support for Android builds](/blog/2022/09/05/version-070#full-cmake-support-for-android-builds)
- [Highlights of 0.70](/blog/2022/09/05/version-070#highlights-of-070)

<!--truncate-->

## New Architecture’s New Documentation

Over the last few months, we have been working to add more content to the [New Architecture](/architecture/landing-page) section of the documentation. In the new section you can find migration guides, examples and tutorials to get you up to speed.

Along with it, you can find new documents diving into [Why a New Architecture](https://reactnative.dev/docs/next/the-new-architecture/why) and [the various parts of it](https://reactnative.dev/docs/next/the-new-architecture/pillars). We hope this helps you better understand the rationale behind the new APIs.

Any feedback is more than welcome, please let us know in the [react-native-website](https://github.com/facebook/react-native-website) repository.

## Hermes as default engine

React Native 0.70 is the first version with Hermes, our in-house JS engine, enabled by default.

This is the result of collaborative effort between the Hermes team and the React Native team, alongside with the priceless contributions from the community. We worked to improve and fine tune Hermes to make it more performant and deliver highly requested features by the community.

You can read more about it in the [official announcement blogpost](https://reactnative.dev/blog/2022/07/08/hermes-as-the-default).

## A new unified configuration for Codegen

With 0.70, we introduced a unified way to define the Codegen specs for both iOS and Android. Previously, you had to put the Android configuration in a separate `build.gradle` file.

Now, you can define it directly in the package.json with:

```json
  "codegenConfig": {
    "name": "CustomAnimationView",
    "type": "components",
    "jsSrcsDir": "./src",
    "android": {
      "javaPackageName": "com.custom.animation"
    }
  }
```

This improvement provides a more consistent experience for library maintainers in migrating their codebases to the New Architecture.

If you are a library maintainer, please make sure to let us know how the process is going for you in [this discussion](https://github.com/reactwg/react-native-new-architecture/discussions/6) in the [React Native New Architecture working group](https://github.com/reactwg/react-native-new-architecture).

## Android Auto-linking for New Architecture libraries

With 0.70, users on New Architecture are able to automatically link libraries without any additional configuration on their Android.mk or CMake files.

Autolinking is a crucial part of the React Native development experience. It allows you to include external libraries with a `yarn add` command, without dealing with CocoaPods or Gradle setups.

The New Architecture required us to adapt the auto-linking features to support libraries which are using the Codegen and exposing native code to app developers.

While Autolinking worked well for New Architecture libraries on iOS, the same was not true for Android. With 0.70 we closed this gap and you can now keep on including libraries with `yarn add` to your project: they will be linked correctly on any architecture.

## Full CMake support for Android builds

Starting from 0.70, users can now use CMake to configure their Native builds. While we don’t expect app users to directly write C++ code, you still need an entry point for the native compilation.

From now on you can use a `CMakeLists.txt` file instead of an `Android.mk` file for anything Android/Native related in your project.

This change benefits both app and library users on the New Architecture as:

- The CMake file created in your app is way smaller ([3 lines of code](https://github.com/facebook/react-native/blob/9923ac1b524ae959abdf50a28a3094198015f77e/packages/rn-tester/android/app/src/main/jni/CMakeLists.txt#L6-L11) versus [50+ for Android.mk files](https://github.com/facebook/react-native/blob/main/template/android/app/src/main/jni/Android.mk?rgh-link-date=2022-07-20T18%3A29%3A07Z)). This makes for an easier update experience between React Native versions in the future and less code to maintain on your end.
- Codegen is now generating both `Android.mk` and `CMakeLists.txt`, so libraries should not worry about doing anything if they're using the default setup we provide for New Architecture libraries.
- The Auto-linking mentioned above will work with both CMake and Android.mk files out of the box.
- Despite apps being free to use either `Android.mk` or CMake files, the recommended solution in the future would be CMake files (due to better documentation, tooling and ecosystem around CMake).

## Highlights of 0.70

As mentioned above, some of the more important improvements in this release are centered around the New Architecture experience. However, there have been other notable changes, including:

- Fix for Catalyst is live, set `mac_catalyst_enabled` to `true` in Podfile (see [upgrade-helper](https://react-native-community.github.io/upgrade-helper/?from=0.69.1&to=0.70.0-rc.0) diff for details).
- Bumping metro to 0.72.0 which will enable the new React JSX Transform: [reactjs.org/blog/2020/09/22/introducing-the-new-jsx-transform.html](https://reactjs.org/blog/2020/09/22/introducing-the-new-jsx-transform.html).
- Removing `reactnativeutilsjni` as it is built from the same sources as `reactnativejni` which results in ~220 KBs saved from every Android APK build. ([https://github.com/facebook/react-native/pull/34339](https://github.com/facebook/react-native/pull/34339)).

### Breaking changes

There have also been a few breaking changes:

- Removed jest/preprocessor from the react-native package ([0301cb285b](https://github.com/facebook/react-native/commit/0301cb285b2e85b48a397fe58d565196654d9754) by [@motiz88](https://github.com/motiz88))
- Remove nonstandard `Promise.prototype.done` ([018d5cf985](https://github.com/facebook/react-native/commit/018d5cf985497273dd700b56168cf1cf64f498d5) by [@motiz88](https://github.com/motiz88))

Please also note that the version of Metro has been bumped to 0.72, which comes [with 5 breaking changes](https://github.com/facebook/metro/releases/tag/v0.72.0).

### Upgrades

And we upgraded some of our dependencies:

- Bump RN CLI to v9.0.0
- Bump Android Gradle Plugin to 7.2.1
- Bump Gradle to 7.5.1
- Bump RCT-Folly to 2021-07-22
- Bump Metro to 0.72
- Bump SoLoader to 0.10.4

You can check out the full list of changes [in the changelog](https://github.com/facebook/react-native/blob/main/CHANGELOG.md).

### Acknowledgements

88 contributors with their 493 commits have helped to make this release possible - thanks everyone! We are also thankful to everyone else who gave their feedback to ensure this release would be as stable as possible.
