---
title: Announcing React Native 0.60
author: <PERSON>
authorTitle: Core Maintainer & React Native Developer
authorURL: 'https://twitter.com/turnrye'
authorImageURL: 'https://avatars0.githubusercontent.com/u/701035?s=460&v=4'
authorTwitter: turnrye
tags: [announcement, release]
---

After months of hard work from hundreds of contributors, the React Native Core team is proud to announce the release of version 0.60. This release handles significant migrations for both Android and iOS platforms, and many issues are resolved too. This blog post covers the highlights of the release. As always though, refer to the changelog for more detailed information. Finally, thank you contributors for helping us to make this milestone!

## Focus on Accessibility

There have been many improvements to the accessibility APIs, like [announceForAccessibility](https://github.com/facebook/react-native/commit/cfe0032), plus improvements to [roles](https://github.com/facebook/react-native/commit/1aeac1c), [action support](https://github.com/facebook/react-native/commit/14b4668), [flags](https://github.com/facebook/react-native/commit/0090ab3), and more. Accessibility is a complex science, but we hope these improvements make it a bit easier to be an A11Y. Be sure to check [React Native Open Source Update June 2019](/blog/2019/06/12/react-native-open-source-update#meaningful-community-contributions) for more details of these changes.

## A Fresh Start

React Native's start screen has been updated! Thank you to the many contributors who helped create the new UI. This new "Hello World" will welcome users to the ecosystem in a more friendly, engaging way.

![The new init screen helps developers get started from the get-go with resources and a good example](/blog/assets/0.60-new-init-screen.png)

## AndroidX Support

[AndroidX](https://developer.android.com/jetpack/androidx) is a major step forward in the Android ecosystem, and the old support library artifacts are being deprecated. For 0.60, React Native has been migrated over to AndroidX. This is a breaking change, and **your native code and dependencies will need to be migrated** as well.

> With this change, React Native apps will need to begin using AndroidX themselves. They cannot be used side-by-side in one app, so all of the app code and dependency code needs to be using one or the other.
>
> [matt-oakes](https://github.com/matt-oakes) on [discussions-and-proposals](https://github.com/react-native-community/discussions-and-proposals/issues/129)

While your own native code will need to be migrated by you, [@mikehardy](https://github.com/mikehardy), [@cawfree](https://github.com/cawfree), and [@m4tt72](https://github.com/m4tt72) built a [clever tool named "jetifier"](https://github.com/mikehardy/jetifier) to patch your `node_modules`. Library maintainers will need to upgrade, but this tool provide you with a temporary solution while giving them time to release an AndroidX version. So if you find errors related to AndroidX migration, give this a shot.

## CocoaPods by Default

[CocoaPods are now part of React Native's iOS project](https://github.com/react-native-community/discussions-and-proposals/blob/master/proposals/0004-cocoapods-support-improvements.md). If you weren't already, be sure to open iOS platform code using the `xcworkspace` file from now on (protip: try `xed ios` from the root project directory). Also, the `podspec`s for the internal packages have changed to make them compatible with the Xcode projects, which will help with troubleshooting and debugging. Expect to make [some straightforward changes](https://github.com/facebook/react-native/commit/2321b3f) to your `Podfile` as part of the upgrade to 0.60 to bring this exciting support. Note that we are aware of a compatibility issue with `use_frameworks!`, and we're tracking an [issue](https://github.com/facebook/react-native/issues/25349) with workarounds and a future patch.

## Lean Core Removals

**WebView** and **NetInfo** were previously extracted into separate repositories, and in 0.60 we’ve finished migrating them out of the React Native repository. Additionally, in response to community feedback about new App Store policy, **Geolocation** has been extracted. If you haven’t already, complete your migration by adding dependencies to [react-native-webview](https://github.com/react-native-community/react-native-webview), [@react-native-community/netinfo](https://github.com/react-native-community/react-native-netinfo), and [@react-native-community/geolocation](https://github.com/react-native-community/react-native-geolocation). If you'd like an automated solution, consider using [rn-upgrade-deprecated-modules](https://github.com/lucasbento/rn-update-deprecated-modules). Maintainers have made more than 100 commits to these repositories since extraction and we’re excited to see the community’s support!

## Native Modules are now Autolinked

The team working on the [React Native CLI](https://github.com/react-native-community/cli) has introduced major improvements to native module linking called [autolinking](https://github.com/react-native-community/cli/blob/master/docs/autolinking.md)! Most scenarios will not require the use of `react-native link` anymore. At the same time, the team overhauled the linking process in general. Be sure to `react-native unlink` any preexisting dependencies as mentioned in the docs above.

## Upgrade Helper

[@lucasbento](https://github.com/lucasbento), [@pvinis](https://github.com/pvinis), [@kelset](https://github.com/kelset), and [@watadarkstar](https://github.com/watadarkstar) have built a great tool called [Upgrade Helper](https://react-native-community.github.io/upgrade-helper/) to make the upgrade process simpler. It helps React Native users with brownfield apps or complex customizations to see what's changed between versions. Take a look at the [updated upgrading docs](/docs/upgrading) and try it out today for your upgrade path!

![Upgrade Helper cleanly and easily shows the changes needed to migrate to a different version of React Native](/blog/assets/0.60-upgrade-helper.png)

## A Note to Library Maintainers

Changes for AndroidX will almost certainly require updates to your library, so be sure to include support soon. If you're not able to upgrade yet, consider checking your library against the jetifier to confirm that users are able to patch your library at build time.

Review the [autolinking](https://github.com/react-native-community/cli/blob/master/docs/autolinking.md) docs to update your configs and readme. Depending on how your library was previously integrated, you may also need to make some additional changes. Check the [dependencies](https://github.com/react-native-community/cli/blob/master/docs/dependencies.md) guide from the CLI for information on how to define your dependency interface.

## Thanks

While these are the highlights that we noted, there are many others to be excited about. To see all the updates, take a look at the [changelog](https://github.com/react-native-community/react-native-releases/blob/master/CHANGELOG.md). As always, stay tuned for more news. Enjoy 0.60 in the meantime!
