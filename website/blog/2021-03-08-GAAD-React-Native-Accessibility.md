---
title: The GAAD Pledge - Improving React Native Accessibility
authors: [alexmarlette]
tags: [announcement]
---

## Hello React Native Community,

In May 2020 Facebook was the first company to take the [GAAD pledge](https://diamond.la/GAADPledge/), by doing so they committed to making accessibility a core part of the React Native open source project. Since May, Facebook has spent that time thoughtfully reviewing and documenting accessibility gaps within React Native. So far the gap analysis has surfaced 90 issues, all of which have been translated to [GitHub issues](https://github.com/facebook/react-native/projects/15).

Overall, we found that React Native APIs provide strong support for accessibility. However, we also found many core components do not yet fully utilize platform accessibility APIs and support is missing for some platform specific features.

The enthusiasm and diversity of contributors have always played a critical role in the development of React Native and these gaps in accessibility are great opportunities for current and new contributors. If you have been interested in contributing to React Native, we encourage you to join us in making React Native more accessible.

To recognize contributors for their effort, when an accessibility issue is closed and attached to a pull request, contributors will get a shout out on Twitter from our community manager. Contributors whose pull requests are accepted into the codebase will be highlighted in our monthly issues update on the React Native blog.

Please join us in making React Native more accessible for everyone.

### How you can help:

- New contributors should read the [contribution guide](https://github.com/facebook/react-native/blob/master/CONTRIBUTING.md) and browse the list of 46 [good first issues](https://github.com/facebook/react-native/issues?q=is%3Aopen+is%3Aissue+label%3A%22Good+first+issue%22+label%3AAccessibility) in the React Native GitHub.

- Contributors interested in issues requiring a bit more effort should visit [the project page for Improved React Native Accessibility](https://github.com/facebook/react-native/projects/15) to see the GitHub issues that need their knowledge of React Native.

- Technical writers interested in updating React Native's documentation to reflect the accessibility gaps being closed should visit the [React Native Docs](https://github.com/facebook/react-native-website#-overview).

- Share this initiative with anyone who may be able to help!

- Follow the GAAD Pledge Open Source Accessibility Community Manager for React Native on [Twitter](https://twitter.com/alexmarlette) or [Facebook](https://www.facebook.com/React-Native-Open-Source-Accessibility-Community-Manager-102732258549941) to keep up to date on progress.
