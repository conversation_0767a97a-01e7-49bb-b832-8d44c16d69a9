---
title: React Native in H2 2021
authors: [lunaleaps]
tags: [announcement]
---

Over the past year so much has changed in our world, React Native being no exception. We've welcomed new members to our team (whom we are excited to eventually meet in person!), our projects have matured and new opportunities have arisen. We're excited to share all this with you in this post and others to come!

At Facebook, our team works in half-year cycles. Each half we review our strategy, set plans, and share them internally. Today, we want to share our H2 plans with you, our community.

H2 2021 is an exciting half for React Native. Our areas of focus include nurturing the community, beginning to roll out the new architecture to open source, and pushing the technology forward.

<!--truncate-->

## Setting up the Community for Success

React Native’s open source ecosystem is one of its greatest strengths. We’ve identified the following areas as the best ways to grow our partnership with the community.

### Community Engagement

In order for everyone to move in the same direction, we want to continually communicate our vision and status to the community. Sharing our incremental progress publicly isn’t second-nature for us (yet). This half, we are committing to a more consistent content schedule that exercises the muscle and sets up processes to reduce friction. Beyond sharing our updates, we also want to use this opportunity to showcase the accomplishments of the community.

### New Architecture Rollout and Releases

In H1, [we finished rolling out the new React Native architecture](https://twitter.com/reactnative/status/1415099806507167745) to all React Native mobile products in the Facebook app. Facebook is made up of surfaces like Marketplace Tab and Dating Profile, and we have migrated over 1000 surfaces!

In H2, we are beginning to bring this new architecture to the community. We have put together a playbook that shows how to migrate libraries and applications. We have already shared drafts with early partners that maintain popular React Native libraries. We expect to get their feedback, support them in upgrading their libraries, and learn how to improve our playbook and implementations. We plan on sharing the playbook more broadly as soon as the work stabilizes.

We are also focused on improving React Native’s release process. We will need frequent reliable releases to progressively ship the new React Native architecture. Our aim is to catch release-blockers earlier and to reduce the turn-around time. This will enable more predictable release timelines with less burden on contributors and developers.

### Repository Health

When developers evaluate a technology, an essential signal is the health of its repository. This impression is informed by signals such as pull request throughput, stars, age of last commit, and others. The React Native repository has historically lacked a process to ensure that issues and pull requests are reviewed in a timely manner. Our work this half is two-fold: we need to resolve a large backlog of contributions, and we need to build sustainable practices for handling incoming contributions. We hope this work will also set us up for success for more engagement from the community via issues and PRs as the community starts using the new architecture. You can follow our progress on the backlog using this [project board.](https://github.com/facebook/react-native/projects/17)

## Pushing the Technology Forward

### New Architectural Capabilities

As mentioned, our new architecture recently finished rolling out to the Facebook mobile apps! We are already building new capabilities that will change the performance curve for React Native. This includes supporting Concurrent Rendering in React Native. Concurrent Rendering enables React to pause and resume work and quickly respond to high priority events like a touch gesture. Something we are also really excited about is using Concurrent Rendering to make use of idle CPU cycles for rendering offscreen product features without slowing down onscreen user interactions.

### Mobile and Beyond

We have had overwhelming success with powering mobile experiences using React at Facebook, and we have seen amazing mobile products from our partners. Now we want to bring that success to more platforms! We believe that targeting many platforms lets us learn new lessons and further improve our offering on mobile platforms. (We will share more about this in a later post.) The exciting news is that we’ve already started taking steps towards making this a reality!

#### Messenger Desktop

Last summer, we [expanded our focus beyond mobile](https://twitter.com/reactnative/status/1286061933293010944) by partnering with Microsoft to accelerate React Native development on Windows and macOS. We partnered with the Messenger team at Facebook to enable user experiences that are only possible on desktop, and to deliver huge performance wins over their previous Electron implementation. Our vision is to combine high quality, delightful, _native_ desktop user experiences with the great developer experience of React Native.

The Messenger team has ambitious plans for the Messenger desktop apps, including finding new ways to further unleash the unique capabilities of desktop platforms and large screens. We are excited to continue enabling these experiences by investing more into React Native on desktop.

#### React Native in Virtual Reality

Oculus has long used React Native to power core experiences like Store, where people buy applications and games in VR. In H2, React Native and Oculus are partnering to bring new exciting experiences to VR. We are also bringing optimizations and improvements from VR to mobile, and from mobile to VR. This will include expanding React Native to support unique requirements of VR — from new input types like controllers to new memory usage optimizations. This is the start of an exciting journey to explore and influence how immersive experiences can built for VR using React Native.

### Summary

<!--alex ignore gross-->

We hope our H2 plans excite you as much as it does us, and we look forward to sharing more with you in the upcoming months! We will also be attending React Native EU on September 1, 2021 where teammate Joshua Gross ([@joshuaisgross](https://twitter.com/joshuaisgross)) will share how we replaced the architecture for the world’s largest React Native app. [Register for React Native EU](https://www.react-native.eu/) if you haven’t already! In the meantime, connect with us on Twitter ([@reactnative](https://twitter.com/reactnative)) for updates on events, our roadmap, and much more.
