---
name: '💡 Website Other'
about: Post an idea, propose improvement, start discussion or put a request according to React Native website.
title: ''
---

## Description

<!-- Please provide a clear and concise description of your idea, a "TLDR." You can go into more detail below—more details make issues more actionable for us and help us prioritize! Include screenshots if needed. -->

## What is the problem?

<!-- Does this solve a problem? If so, what is it? -->

## How can we address it?

<!-- Are there any actionable steps we can take to rectify the situation? -->

## Why is it important?

<!-- Make your case here! -->

## Who needs this?

<!-- Android devs? New learners? TypeScript users? -->

## When should this happen (use version numbers if needed)?
