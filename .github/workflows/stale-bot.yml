name: <PERSON> stale issues and pull requests
on:
  schedule:
    - cron: "30 1 * * *"
jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v4
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          days-before-issue-stale: 90
          days-before-pr-stale: -1
          stale-issue-message: "👋 Hey there, it looks like there has been no activity on this issue in the last 90 days. Has the issue been fixed, or does it still require the community attention? This issue will be closed in the next 7 days if no further activity occurs. Thank you for your contributions."
          close-issue-message: "Closing this issue after a prolonged period of inactivity. If this issue is still present in the latest release, please feel free to create a new issue with up-to-date information."
          exempt-issue-labels: 👋 Good first issue, 👻 Missing Docs, Wait on future Release, Never gets stale
