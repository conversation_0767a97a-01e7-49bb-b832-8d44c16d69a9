name: Pre-merge

on:
  pull_request:
    branches:
      - main

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Enable Corepack
        run: corepack enable

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "yarn"

      - name: Check dependencies
        run: yarn dedupe --check

      - name: Install dependencies
        run: yarn install --immutable

      - name: Run Lint
        run: yarn ci:lint
        working-directory: website

  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Enable Corepack
        run: corepack enable

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "yarn"

      - name: Install dependencies
        run: yarn install --immutable

      - name: Build
        run: yarn build
        working-directory: website
        env:
          NODE_OPTIONS: "--max_old_space_size=8192"
