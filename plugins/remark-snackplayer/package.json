{"name": "@react-native-website/remark-snackplayer", "version": "0.1.0", "private": true, "description": "Remark Expo Snack Plugin", "main": "src/index.js", "keywords": ["remark", "react-native", "expo", "snackplayer", "preview"], "files": ["src"], "scripts": {"prettier": "prettier --write '{src,tests}/**/*.{md,js,jsx,ts,tsx}'", "test": "yarn tape tests/index.js"}, "dependencies": {"dedent": "^1.5.3", "object.fromentries": "^2.0.3", "unist-util-visit-parents": "^3.1.1"}, "devDependencies": {"remark": "^15.0.1", "tape": "^5.7.0"}}