<div
  class="snack-player"
  data-snack-name="FirstPlayer"
  data-snack-description="Example usage"
  data-snack-code="import%20React%20from%20'react'%3B%0Aimport%20%7B%20Text%2C%20View%20%7D%20from%20'react-native'%3B%0A%0Aconst%20YourApp%20%3D%20()%20%3D%3E%20%7B%0A%20%20%20%20return%20(%0A%20%20%20%20%3CView%20style%3D%7B%7B%20flex%3A%201%2C%20justifyContent%3A%20%22center%22%2C%20alignItems%3A%20%22center%22%20%7D%7D%3E%0A%20%20%20%20%20%20%20%20%3CText%3E%0A%20%20%20%20%20%20%20%20Try%20editing%20me!%20%F0%9F%8E%89%0A%20%20%20%20%20%20%20%20%3C%2FText%3E%0A%20%20%20%20%3C%2FView%3E%0A%20%20%20%20)%3B%0A%7D%0A%0Aexport%20default%20YourApp%3B"
  data-snack-dependencies=""
  data-snack-platform="web"
  data-snack-supported-platforms="ios,android,web"
  data-snack-theme="light"
  data-snack-preview="true"
  data-snack-loading="lazy"
  data-snack-device-frame="false"
></div>

<div
  class="snack-player"
  data-snack-name="SecondPlayer"
  data-snack-description="Example usage"
  data-snack-code="import%20React%20from%20'react'%3B%0Aimport%20%7B%20View%20%7D%20from%20'react-native'%3B%0Aimport%20Slider%20from%20'%40react-native-community%2Fslider'%3B%0A%0Aconst%20YourApp%20%3D%20()%20%3D%3E%20%7B%0A%20%20%20%20return%20(%0A%20%20%20%20%3CView%20style%3D%7B%7B%20flex%3A%201%2C%20justifyContent%3A%20%22center%22%2C%20alignItems%3A%20%22center%22%20%7D%7D%3E%0A%20%20%20%20%20%20%20%20%3CSlider%0A%20%20%20%20%20%20%20%20%20%20style%3D%7B%7Bwidth%3A%20200%2C%20height%3A%2040%7D%7D%0A%20%20%20%20%20%20%20%20%20%20minimumValue%3D%7B0%7D%0A%20%20%20%20%20%20%20%20%20%20maximumValue%3D%7B1%7D%0A%20%20%20%20%20%20%20%20%20%20minimumTrackTintColor%3D%22%23FFFFFF%22%0A%20%20%20%20%20%20%20%20%20%20maximumTrackTintColor%3D%22%23000000%22%0A%20%20%20%20%20%20%20%20%2F%3E%0A%20%20%20%20%3C%2FView%3E%0A%20%20%20%20)%3B%0A%7D%0A%0Aexport%20default%20YourApp%3B"
  data-snack-dependencies="@react-native-community/slider"
  data-snack-platform="web"
  data-snack-supported-platforms="ios"
  data-snack-theme="dark"
  data-snack-preview="false"
  data-snack-loading="eager"
  data-snack-device-frame="false"
></div>
